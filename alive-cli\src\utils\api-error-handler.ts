import chalk from "chalk";
import { providers } from "./providers";

/**
 * Handle API errors in a consistent way across the application
 * @param error The error object from the API call
 * @param provider The provider that was used for the API call
 * @returns A formatted error message and suggested actions
 */
export function handleApiError(error: any, provider: string): string {
  const normalizedProvider = provider.toLowerCase();
  const providerInfo = providers[normalizedProvider];
  
  // Default error message
  let errorMessage = `Error connecting to ${providerInfo?.name || provider}`;
  let errorDetails = "";
  let suggestedAction = "";
  
  // Extract error information
  if (error && typeof error === 'object') {
    // Handle OpenAI API error format
    if (error.response) {
      const status = error.response.status;
      const errorType = error.response.data?.error?.type;
      const errorCode = error.response.data?.error?.code;
      const message = error.response.data?.error?.message;
      
      errorDetails = message || error.message || String(error);
      
      // Handle common error types
      if (status === 401) {
        if (errorCode === "invalid_api_key") {
          errorMessage = `${chalk.red(`Invalid API key for ${providerInfo?.name || provider}`)}`;
          suggestedAction = `Please check your ${providerInfo?.envKey || `${provider.toUpperCase()}_API_KEY`} environment variable.`;
        } else {
          errorMessage = `${chalk.red(`Authentication error with ${providerInfo?.name || provider}`)}`;
          suggestedAction = `Please verify your API key and permissions.`;
        }
      } else if (status === 404) {
        errorMessage = `${chalk.red(`Resource not found (404) at ${providerInfo?.name || provider}`)}`;
        suggestedAction = `The requested endpoint or model might not exist.`;
      } else if (status === 429) {
        errorMessage = `${chalk.red(`Rate limit exceeded for ${providerInfo?.name || provider}`)}`;
        suggestedAction = `Please wait and try again later or check your usage limits.`;
      } else if (status >= 500) {
        errorMessage = `${chalk.red(`Server error (${status}) from ${providerInfo?.name || provider}`)}`;
        suggestedAction = `This is likely a temporary issue with the provider. Please try again later.`;
      }
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorMessage = `${chalk.red(`Cannot connect to ${providerInfo?.name || provider} API`)}`;
      errorDetails = error.message || String(error);
      suggestedAction = `Please check your internet connection or the provider's service status.`;
    } else if (error.message) {
      errorDetails = error.message;
    } else {
      errorDetails = String(error);
    }
  } else if (error) {
    errorDetails = String(error);
  }
  
  // Format the full error message
  const fullMessage = [
    errorMessage,
    errorDetails ? `${chalk.dim(errorDetails)}` : '',
    suggestedAction ? `\n${chalk.yellow(suggestedAction)}` : '',
    `\nTry switching to a different provider with: ${chalk.cyan(`alive --provider <provider>`)}`
  ].filter(Boolean).join('\n');
  
  return fullMessage;
}

/**
 * Check if an API key is valid for a given provider
 * @param provider The provider to check
 * @returns Boolean indicating if the API key is valid and set
 */
export async function validateApiKey(provider: string): Promise<boolean> {
  const normalizedProvider = provider.toLowerCase();
  
  // Special case for providers that don't require API keys
  if (normalizedProvider === 'ollama') {
    return true;
  }
  
  const envKey = providers[normalizedProvider]?.envKey || `${provider.toUpperCase()}_API_KEY`;
  const apiKey = process.env[envKey];
  
  if (!apiKey || apiKey.trim() === '') {
    return false;
  }
  
  // For simplicity, we're just checking if the key exists and is not empty
  // A more thorough validation would involve making a test API call
  return true;
}

/**
 * Get a list of configured providers (with valid API keys)
 * @returns Array of provider IDs that have valid API keys
 */
export async function getConfiguredProviders(): Promise<string[]> {
  const configuredProviders: string[] = [];
  
  for (const [providerId, providerInfo] of Object.entries(providers)) {
    if (providerId === 'ollama' || process.env[providerInfo.envKey]) {
      configuredProviders.push(providerId);
    }
  }
  
  return configuredProviders;
} 