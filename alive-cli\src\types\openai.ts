// Type definitions for OpenAI responses
// This file provides type definitions for OpenAI API responses
// to fix the TypeScript errors related to missing modules

export interface ResponseOutputText {
  type: 'text' | 'output_text' | 'input_text';
  text: string;
  annotations?: Array<any>;
}

export interface ResponseOutputRefusal {
  type: 'refusal';
  refusal: string;
}

export interface ResponseOutputImage {
  type: 'input_image';
  image_url?: string;
}

export interface ResponseOutputFile {
  type: 'input_file';
  filename: string;
}

export type ResponseOutputContent =
  | ResponseOutputText
  | ResponseOutputRefusal
  | ResponseOutputImage
  | ResponseOutputFile;

// Base ResponseItem interface
export interface ResponseItem {
  id: string;
  type?: string;
  role?: string;
  content?: Array<any>;
  status?: string;
  metadata?: Record<string, any>;
  index?: number;
  call_id?: string;
  output?: any;
}

// Message-specific ResponseItem
export interface ResponseInputMessageItem extends Omit<ResponseItem, 'type'> {
  type?: "message";
  role: string;
  content: Array<ResponseOutputContent>;
}

// Output message-specific ResponseItem
export interface ResponseOutputMessage extends ResponseItem {
  type: "message";
  role: string;
  content: Array<ResponseOutputContent>;
}

// Reasoning-specific ResponseItem
export interface ResponseReasoningItem extends ResponseItem {
  type: "reasoning";
  summary: Array<{
    headline?: string;
    text: string;
  }>;
}

// Function call specific ResponseItem
export interface ResponseFunctionToolCallItem extends Omit<ResponseItem, 'type'> {
  type?: "function_call";
  name: string;
  arguments: string;
}

// Function call output specific ResponseItem
export interface ResponseFunctionToolCallOutputItem extends ResponseItem {
  type: "function_call_output";
  call_id: string;
  output: any;
}

// Local shell call specific ResponseItem
export interface ResponseLocalShellCallItem extends ResponseItem {
  type: "local_shell_call";
  status: string;
  action: {
    type: string;
    working_directory?: string;
    command: string[];
  };
}

// Local shell call output specific ResponseItem
export interface ResponseLocalShellCallOutputItem extends ResponseItem {
  type: "local_shell_call_output";
  call_id: string;
  output: any;
}

export interface ChatCompletionChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      content?: string;
      role?: string;
      function_call?: {
        name?: string;
        arguments?: string;
      };
      tool_calls?: Array<{
        index: number;
        id?: string;
        type: string;
        function: {
          name?: string;
          arguments?: string;
        };
      }>;
    };
    finish_reason: string | null;
  }>;
}

export interface ChatCompletion {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string | null;
      function_call?: {
        name: string;
        arguments: string;
      };
      tool_calls?: Array<{
        id: string;
        type: string;
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface FileOpenerScheme {
  type: string;
  path?: string;
}

// Type for batch entries in message history
export type BatchEntry = {
  item?: ResponseItem;
  group?: GroupedResponseItem;
};

// Type for grouped response items
export interface GroupedResponseItem {
  label: string;
  items: Array<ResponseItem>;
}
