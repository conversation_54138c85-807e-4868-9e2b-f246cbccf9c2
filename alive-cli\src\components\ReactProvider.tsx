import React, { ReactNode, createContext, useState, useEffect, useRef, useContext, useReducer, useCallback, useMemo } from 'react';

/**
 * Create safe versions of React hooks that won't crash the application
 * when used incorrectly or in non-function components
 */
function createSafeHook<T>(hookName: string, defaultValue: T): any {
  const originalHook = (React as any)[hookName];

  return function(...args: any[]) {
    try {
      // Check if we're in a valid React component context
      if (typeof React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current !== 'undefined') {
        return originalHook.apply(React, args);
      } else {
        // If we're not in a component context, return the default value
        console.warn(`React hook warning: ${hookName} called outside component context. Using fallback.`);
        return defaultValue;
      }
    } catch (error) {
      // If it fails, log a warning and return a default value
      console.warn(`React hook warning: ${hookName} failed. This is expected during startup.`);
      return defaultValue;
    }
  };
}

// Enhanced safe versions of commonly used hooks with better defaults
const safeHooks = {
  useState: createSafeHook('useState', [null, () => {}]),
  useEffect: createSafeHook('useEffect', undefined),
  useRef: createSafeHook('useRef', { current: null }),
  useContext: createSafeHook('useContext', {}),
  useReducer: createSafeHook('useReducer', [null, () => {}]),
  useCallback: createSafeHook('useCallback', () => {}),
  useMemo: createSafeHook('useMemo', null),
  // Add additional hooks that might be used
  useLayoutEffect: createSafeHook('useLayoutEffect', undefined),
  useImperativeHandle: createSafeHook('useImperativeHandle', undefined),
  useDebugValue: createSafeHook('useDebugValue', undefined),
};

/**
 * Create a React context to ensure hooks work properly
 * This provides a safe way to access React hooks from anywhere
 */
export const ReactContext = createContext<{
  useState: typeof React.useState;
  useEffect: typeof React.useEffect;
  useRef: typeof React.useRef;
  useContext: typeof React.useContext;
  useReducer: typeof React.useReducer;
  useCallback: typeof React.useCallback;
  useMemo: typeof React.useMemo;
  useLayoutEffect: typeof React.useLayoutEffect;
  useImperativeHandle: typeof React.useImperativeHandle;
  useDebugValue: typeof React.useDebugValue;
}>(safeHooks);

/**
 * ReactProvider Component
 *
 * This component ensures that React hooks are properly initialized
 * by providing a valid React component context.
 *
 * It also initializes all common hooks to ensure they're available
 * throughout the application.
 */
interface ReactProviderProps {
  children: ReactNode;
}

/**
 * A simple reducer function for useReducer
 */
function dummyReducer(state: any, action: any) {
  return state;
}

// Apply the safe hooks globally to prevent crashes
Object.entries(safeHooks).forEach(([hookName, safeHook]) => {
  (React as any)[hookName] = safeHook;
});

export const ReactProvider: React.FC<ReactProviderProps> = ({ children }) => {
  // Initialize all common hooks to ensure they're properly set up
  const [state, setState] = useState({});
  useEffect(() => {}, []);
  const ref = useRef(null);
  const context = useContext(ReactContext);
  const [reducerState, dispatch] = useReducer(dummyReducer, {});
  const callback = useCallback(() => {}, []);
  const memoized = useMemo(() => ({}), []);

  // Create a context with all the hooks, using safe versions
  const contextValue = {
    ...safeHooks
  };

  return (
    <ReactContext.Provider value={contextValue}>
      {children}
    </ReactContext.Provider>
  );
};

export default ReactProvider;
