/**
 * React Patch Utility
 * 
 * This file contains utilities to patch <PERSON>act's rendering to handle specific errors
 * that occur during the rendering process, particularly the "Cannot destructure property 'cursorOffset' of 'u' as it is null" error.
 */

/**
 * Apply patches to React to make it more resilient to errors
 */
export function applyReactPatches() {
  if (typeof window !== 'undefined' && window.React) {
    patchReactCreateElement(window.React);
  }
  
  if (typeof global !== 'undefined' && global.React) {
    patchReactCreateElement(global.React);
  }
  
  // Patch the global React object
  if (typeof React !== 'undefined') {
    patchReactCreateElement(React);
  }
}

/**
 * Patch React's createElement method to catch and handle specific errors
 */
function patchReactCreateElement(ReactInstance: any) {
  if (!ReactInstance || typeof ReactInstance.createElement !== 'function') {
    return;
  }
  
  const originalCreateElement = ReactInstance.createElement;
  
  ReactInstance.createElement = function(...args: any[]) {
    try {
      return originalCreateElement.apply(ReactInstance, args);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Handle the specific cursorOffset error
      if (errorMessage.includes('cursorOffset') || 
          errorMessage.includes('Cannot destructure property')) {
        console.warn('React createElement error suppressed:', errorMessage);
        
        // Return a simple div element as fallback
        return originalCreateElement('div', { 
          key: 'error-fallback',
          style: { padding: '10px', color: 'red' }
        }, 'Error rendering component');
      }
      
      // Re-throw other errors
      throw error;
    }
  };
}

/**
 * Patch React's useState hook to be more resilient
 */
export function patchReactHooks(ReactInstance: any) {
  if (!ReactInstance) return;
  
  const hooks = [
    'useState', 
    'useEffect', 
    'useContext', 
    'useReducer', 
    'useCallback', 
    'useMemo', 
    'useRef',
    'useLayoutEffect',
    'useImperativeHandle',
    'useDebugValue'
  ];
  
  hooks.forEach(hookName => {
    if (typeof ReactInstance[hookName] === 'function') {
      const originalHook = ReactInstance[hookName];
      
      ReactInstance[hookName] = function(...args: any[]) {
        try {
          // Check if we're in a valid React component context
          if (typeof ReactInstance.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current !== 'undefined') {
            return originalHook.apply(ReactInstance, args);
          } else {
            // If we're not in a component context, return a default value
            console.warn(`React hook warning: ${hookName} called outside component context. Using fallback.`);
            
            // Return appropriate default values based on the hook
            switch (hookName) {
              case 'useState': return [null, () => {}];
              case 'useReducer': return [null, () => {}];
              case 'useRef': return { current: null };
              case 'useContext': return {};
              case 'useCallback': return () => {};
              case 'useMemo': return null;
              default: return undefined;
            }
          }
        } catch (e) {
          // If it fails, return a default value
          console.warn(`React hook warning: ${hookName} failed. This is expected during startup.`);
          
          // Return appropriate default values based on the hook
          switch (hookName) {
            case 'useState': return [null, () => {}];
            case 'useReducer': return [null, () => {}];
            case 'useRef': return { current: null };
            case 'useContext': return {};
            case 'useCallback': return () => {};
            case 'useMemo': return null;
            default: return undefined;
          }
        }
      };
    }
  });
}
