import { EventEmitter } from 'events';
import { sanitizePath } from './file-system-utils';

/**
 * Represents an action being performed by the agent
 */
export interface Action {
  /** Unique identifier for the action */
  id: string;
  /** Human-readable description of the action */
  description: string;
  /** Type of action being performed */
  type: 'thinking' | 'processing' | 'file_read' | 'file_write' | 'network' | 'execution' | 'other';
  /** Current status of the action */
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  /** Estimated progress (0-100) */
  progress?: number;
  /** Additional details about the action */
  details?: {
    /** Path of file being operated on */
    path?: string;
    /** Command being executed */
    command?: string;
    /** Error message if action failed */
    error?: string;
    /** Progress message to display */
    progressMessage?: string;
    /** Filename extracted from path (for more concise display) */
    filename?: string;
    /** Operation type for file operations (read, write, error) */
    operation?: string;
    /** Estimated time remaining in seconds */
    estimatedTimeRemaining?: number;
    /** Any other relevant details */
    [key: string]: any;
  };
  /** When the action was created */
  createdAt: Date;
  /** When the action was started */
  startedAt?: Date;
  /** When the action was completed */
  completedAt?: Date;
  /** Sub-actions that are part of this action */
  subActions?: Action[];
}

/**
 * Tracks current and upcoming actions being performed by the agent
 */
export class ActionTracker extends EventEmitter {
  /** Current action being performed */
  private currentAction: Action | null = null;
  /** Queue of upcoming actions */
  private upcomingActions: Action[] = [];
  /** History of completed actions */
  private actionHistory: Action[] = [];
  /** Singleton instance */
  private static instance: ActionTracker;

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance of ActionTracker
   */
  public static getInstance(): ActionTracker {
    if (!ActionTracker.instance) {
      ActionTracker.instance = new ActionTracker();
    }
    return ActionTracker.instance;
  }

  /**
   * Add a new action to the queue
   */
  public addAction(action: Omit<Action, 'id' | 'status' | 'createdAt'>): Action {
    const newAction: Action = {
      ...action,
      id: `action-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      status: 'pending',
      createdAt: new Date(),
    };

    this.upcomingActions.push(newAction);
    this.emit('action-added', newAction);
    this.emit('actions-updated');
    return newAction;
  }

  /**
   * Start the next action in the queue
   */
  public startNextAction(): Action | null {
    if (this.currentAction && this.currentAction.status === 'in_progress') {
      // Complete the current action first
      this.completeCurrentAction();
    }

    if (this.upcomingActions.length === 0) {
      this.currentAction = null;
      this.emit('actions-updated');
      return null;
    }

    const nextAction = this.upcomingActions.shift()!;
    nextAction.status = 'in_progress';
    nextAction.startedAt = new Date();
    this.currentAction = nextAction;

    this.emit('action-started', nextAction);
    this.emit('actions-updated');
    return nextAction;
  }

  /**
   * Complete the current action
   */
  public completeCurrentAction(failed: boolean = false): void {
    if (!this.currentAction) {
      return;
    }

    this.currentAction.status = failed ? 'failed' : 'completed';
    this.currentAction.completedAt = new Date();
    this.actionHistory.push(this.currentAction);

    this.emit('action-completed', this.currentAction, failed);
    this.currentAction = null;
    this.emit('actions-updated');
  }

  /**
   * Update the progress of the current action
   */
  public updateProgress(progress: number): void {
    if (!this.currentAction) {
      return;
    }

    this.currentAction.progress = Math.min(100, Math.max(0, progress));
    this.emit('progress-updated', this.currentAction);
    this.emit('actions-updated');
  }

  /**
   * Update details of the current action
   */
  public updateCurrentActionDetails(details: Partial<Action['details']>): void {
    if (!this.currentAction) {
      return;
    }

    this.currentAction.details = {
      ...this.currentAction.details,
      ...details,
    };

    this.emit('action-updated', this.currentAction);
    this.emit('actions-updated');
  }

  /**
   * Get the current action
   */
  public getCurrentAction(): Action | null {
    return this.currentAction;
  }

  /**
   * Get upcoming actions
   */
  public getUpcomingActions(): Action[] {
    return [...this.upcomingActions];
  }

  /**
   * Get action history
   */
  public getActionHistory(): Action[] {
    return [...this.actionHistory];
  }

  /**
   * Clear all actions
   */
  public clearAll(): void {
    this.currentAction = null;
    this.upcomingActions = [];
    this.actionHistory = [];
    this.emit('actions-cleared');
    this.emit('actions-updated');
  }

  /**
   * Add a file system action based on the operation type
   */
  public addFileSystemAction(operation: 'read' | 'write' | 'error', path?: string, details?: string): Action {
    // Validate and sanitize the path
    const sanitizedPath = sanitizePath(path);
    
    const actionType = operation === 'read' ? 'file_read' :
                      operation === 'write' ? 'file_write' : 'file_write';

    // Create more descriptive messages based on operation type and path
    let description = '';

    if (operation === 'read') {
      description = sanitizedPath
        ? `Reading file: ${sanitizedPath}`
        : 'Reading file';
    } else if (operation === 'write') {
      description = sanitizedPath
        ? `Writing to file: ${sanitizedPath}`
        : 'Writing to file';
    } else { // error
      description = sanitizedPath
        ? `File operation error on: ${sanitizedPath}`
        : 'File operation error';
    }

    // Extract filename from path for more concise display
    const filename = sanitizedPath ? sanitizedPath.split('/').pop()?.split('\\').pop() : undefined;

    return this.addAction({
      description,
      type: actionType,
      details: {
        path: sanitizedPath || undefined,
        filename,
        error: operation === 'error' ? details : undefined,
        operation
      }
    });
  }

  /**
   * Add a progress update with a message
   */
  public addProgressUpdate(progress: number, message?: string): void {
    if (!this.currentAction) {
      return;
    }

    this.updateProgress(progress);

    if (message) {
      this.updateCurrentActionDetails({
        progressMessage: message
      });
    }

    this.emit('progress-updated', this.currentAction);
  }

  /**
   * Add a command execution action
   */
  public addCommandExecutionAction(command: string, workingDirectory?: string): Action {
    const description = `Executing command: ${command}`;

    return this.addAction({
      description,
      type: 'execution',
      details: {
        command,
        workingDirectory
      }
    });
  }

  /**
   * Add a network action
   */
  public addNetworkAction(description: string, url?: string): Action {
    return this.addAction({
      description,
      type: 'network',
      details: {
        url
      }
    });
  }

  /**
   * Add a processing action
   */
  public addProcessingAction(description: string, details?: Record<string, any>): Action {
    return this.addAction({
      description,
      type: 'processing',
      details
    });
  }

  /**
   * Add a thinking action with automatic progress updates
   */
  public addThinkingAction(description: string = 'Processing your request'): Action {
    const action = this.addAction({
      description,
      type: 'thinking'
    });

    // Start a timer to automatically update progress
    const startTime = Date.now();
    const totalTime = 30000; // Assume 30 seconds for completion

    const progressInterval = setInterval(() => {
      const currentAction = this.getCurrentAction();
      if (!currentAction || currentAction.id !== action.id || currentAction.status !== 'in_progress') {
        clearInterval(progressInterval);
        return;
      }

      const elapsed = Date.now() - startTime;
      const progress = Math.min(95, Math.floor((elapsed / totalTime) * 100)); // Cap at 95%

      // Calculate estimated time remaining
      const estimatedTimeRemaining = progress < 10 ? 30 :
                                    Math.ceil((totalTime - elapsed) / 1000);

      this.updateProgress(progress);
      this.updateCurrentActionDetails({
        estimatedTimeRemaining,
        progressMessage: this.getProgressMessage(progress)
      });
    }, 1000);

    return action;
  }

  /**
   * Get a progress message based on the current progress percentage
   */
  private getProgressMessage(progress: number): string {
    if (progress < 20) {
      return 'Analyzing your request...';
    } else if (progress < 40) {
      return 'Processing information...';
    } else if (progress < 60) {
      return 'Generating response...';
    } else if (progress < 80) {
      return 'Finalizing results...';
    } else {
      return 'Almost done...';
    }
  }

  /**
   * Add a sub-action to the current action
   */
  public addSubAction(subAction: Omit<Action, 'id' | 'status' | 'createdAt'>): Action | null {
    if (!this.currentAction) {
      return null;
    }

    const newSubAction: Action = {
      ...subAction,
      id: `subaction-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      status: 'pending',
      createdAt: new Date(),
    };

    if (!this.currentAction.subActions) {
      this.currentAction.subActions = [];
    }

    this.currentAction.subActions.push(newSubAction);
    this.emit('subaction-added', newSubAction, this.currentAction);
    this.emit('actions-updated');

    return newSubAction;
  }
}

// Export singleton instance
export const actionTracker = ActionTracker.getInstance();
