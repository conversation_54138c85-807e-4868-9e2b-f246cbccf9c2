/**
 * Web Tool Keys Utility
 * 
 * This file provides utilities for retrieving and managing API keys for web tools.
 */

import fs from 'fs';
import path from 'path';
import os from 'os';

/**
 * Interface for web tool API keys
 */
export interface WebToolKeys {
  googleApiKey: string;
  bingApiKey: string;
  googleSearchCx: string;
  defaultSearchProvider: 'google' | 'bing' | 'microsoft' | 'yahoo' | 'duckduckgo' | 'stackoverflow' | 'github';
}

/**
 * Get web tool API keys from environment variables or configuration
 */
export function getWebToolKeys(): WebToolKeys {
  // Load from environment variables first
  const keys: WebToolKeys = {
    googleApiKey: process.env.GOOGLE_API_KEY || '',
    bingApiKey: process.env.BING_API_KEY || '',
    googleSearchCx: process.env.GOOGLE_SEARCH_CX || '',
    defaultSearchProvider: (process.env.ALIVE_DEFAULT_SEARCH_PROVIDER || 'duckduckgo') as 'google' | 'bing' | 'microsoft' | 'yahoo' | 'duckduckgo' | 'stackoverflow' | 'github'
  };

  // Try to load from auth.json if environment variables are not set
  try {
    const home = os.homedir();
    const authDir = path.join(home, '.alive-ai');
    const authFile = path.join(authDir, 'auth.json');
    
    if (fs.existsSync(authFile)) {
      const data = JSON.parse(fs.readFileSync(authFile, 'utf-8'));
      
      if (data.webTool) {
        // Only override if environment variables are not set
        if (!keys.googleApiKey && data.webTool.googleApiKey) {
          keys.googleApiKey = data.webTool.googleApiKey;
        }
        
        if (!keys.bingApiKey && data.webTool.bingApiKey) {
          keys.bingApiKey = data.webTool.bingApiKey;
        }
        
        if (!keys.googleSearchCx && data.webTool.googleSearchCx) {
          keys.googleSearchCx = data.webTool.googleSearchCx;
        }
        
        if (!process.env.ALIVE_DEFAULT_SEARCH_PROVIDER && data.webTool.defaultSearchProvider) {
          keys.defaultSearchProvider = data.webTool.defaultSearchProvider;
        }
      }
    }
  } catch (error) {
    console.error('Error loading web tool keys from auth file:', error);
  }

  return keys;
}

/**
 * Save web tool API keys to configuration
 */
export function saveWebToolKeys(keys: Partial<WebToolKeys>): boolean {
  try {
    const home = os.homedir();
    const authDir = path.join(home, '.alive-ai');
    const authFile = path.join(authDir, 'auth.json');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(authDir)) {
      fs.mkdirSync(authDir, { recursive: true });
    }
    
    // Read existing data or create new object
    let data: any = {};
    if (fs.existsSync(authFile)) {
      data = JSON.parse(fs.readFileSync(authFile, 'utf-8'));
    }
    
    // Update web tool keys
    data.webTool = {
      ...(data.webTool || {}),
      ...keys
    };
    
    // Save back to file
    fs.writeFileSync(authFile, JSON.stringify(data, null, 2), 'utf-8');
    
    return true;
  } catch (error) {
    console.error('Error saving web tool keys:', error);
    return false;
  }
}
