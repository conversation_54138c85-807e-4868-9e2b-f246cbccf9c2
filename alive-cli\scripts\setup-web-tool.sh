#!/bin/bash
# <PERSON>ript to set up the web tool dependencies

echo "Setting up Alive AI Web Tool dependencies..."
cd "$(dirname "$0")/.." || exit 1

# Check if npm or pnpm is available
if command -v pnpm >/dev/null 2>&1; then
  PKG_MANAGER="pnpm"
elif command -v npm >/dev/null 2>&1; then
  PKG_MANAGER="npm"
else
  echo "Error: Neither pnpm nor npm is installed. Please install a package manager first."
  exit 1
fi

echo "Using package manager: $PKG_MANAGER"

# Install required dependencies
echo "Installing required dependencies..."
$PKG_MANAGER install axios cheerio node-abort-controller

# Verify installation
if [ $? -eq 0 ]; then
  echo -e "\n✅ Web tool dependencies successfully installed!"
  echo -e "\nYou can now use the web tool with:"
  echo "  alive --enable-web-tool --full-auto \"your prompt here\""
  echo ""
  echo "To configure API keys, you can set them as environment variables:"
  echo "  export GOOGLE_API_KEY=your_api_key"
  echo "  export GOOGLE_SEARCH_CX=your_search_engine_id"
  echo "  export BING_API_KEY=your_api_key"
  echo ""
  echo "Or use them as command-line arguments:"
  echo "  alive --enable-web-tool --google-api-key \"your_api_key\" --google-cx \"your_cx\" \"your prompt\""
  echo ""
  echo "You can also run the demo with:"
  echo "  node alive-cli/examples/web-tool-demo.js"
else
  echo -e "\n❌ Failed to install dependencies. Please try again or install them manually."
  exit 1
fi 