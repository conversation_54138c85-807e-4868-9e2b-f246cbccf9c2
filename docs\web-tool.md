# Web Tool for Alive AI

Alive AI includes a web tool that allows agents to fetch data from the internet, make API calls, and perform web searches. This feature enhances the agent's capabilities by providing access to up-to-date information.

## Features

- **Enabled by Default**: The web tool is now enabled by default for all Alive AI agents.
- **Multiple Search Providers**: Support for Google, Bing, Microsoft, Yahoo, DuckDuckGo, StackOverflow, and GitHub.
- **No API Key Required**: DuckDuckGo, Yahoo, StackOverflow, and GitHub searches work without any API keys.
- **Configurable API Keys**: Easy configuration for Google and Bing API keys.

## Usage

The web tool is enabled by default, so you can use it without any additional flags:

```bash
# For the CLI version
alive "Fetch the latest weather data for New York City"

# For the TUI version
alive-tui "Fetch the latest weather data for New York City"
```

If you need to explicitly enable or disable the web tool, you can use the `--enable-web-tool` flag:

```bash
# Explicitly enable web tool
alive --enable-web-tool "Search for information about Rust programming"

# Disable web tool
alive --enable-web-tool=false "Work without internet access"
```

You also need to ensure that network access is allowed in the sandbox policy. You can do this by adding the `network-full-access` permission:

```bash
alive --sandbox-permissions network-full-access "Search for information about Rust programming"
```

## Configuration

The web tool supports various search providers and API configurations. You can configure the web tool in several ways:

### Command Line Configuration

You can configure the web tool using command line flags:

```bash
# Configure Google search
alive --google-api-key "your-google-api-key" --google-cx "your-custom-search-engine-id" --search-provider google "Search for information about Rust programming"

# Configure Bing search
alive --bing-api-key "your-bing-api-key" --search-provider bing "Search for information about Python programming"

# Use DuckDuckGo (no API key required)
alive --search-provider duckduckgo "Search for information about JavaScript programming"

# Use Yahoo (no API key required)
alive --search-provider yahoo "Search for information about TypeScript programming"

# Use StackOverflow (no API key required)
alive --search-provider stackoverflow "Search for code examples in C#"

# Use GitHub (no API key required)
alive --search-provider github "Search for open source projects in Rust"
```

### Interactive Configuration

When you enable the web tool for the first time without any API keys configured, Alive AI will prompt you to configure the API keys interactively.

### Configuration File

You can also configure the web tool in the `~/.alive-ai/config.json` file:

```json
{
  "enableWebTool": true,
  "webTool": {
    "googleApiKey": "your-google-api-key",
    "bingApiKey": "your-bing-api-key",
    "googleSearchCx": "your-custom-search-engine-id",
    "defaultSearchProvider": "duckduckgo" // Options: "google", "bing", "microsoft", "yahoo", "duckduckgo", "stackoverflow", "github"
  }
}
```

### Environment Variables

You can set the following environment variables:

```bash
export GOOGLE_API_KEY="your-google-api-key"
export BING_API_KEY="your-bing-api-key"
export GOOGLE_SEARCH_CX="your-custom-search-engine-id"
export ALIVE_DEFAULT_SEARCH_PROVIDER="google"  # Options: "google", "bing", "microsoft", "yahoo", "duckduckgo", "stackoverflow", "github"
```

## Usage in Agent Prompts

The web tool can be used by the agent to perform various web operations. Here are examples of how the agent can use the web tool:

### Fetch Operation

Fetches content from a URL:

```javascript
const result = await web({
  operation: "fetch",
  url: "https://example.com",
  method: "GET",
  headers: {
    "User-Agent": "AliveAI/1.0"
  },
  responseType: "text",
  timeout: 5000
})
```

### Scrape Operation

Scrapes content from a webpage with optional selector filtering:

```javascript
const result = await web({
  operation: "scrape",
  url: "https://example.com",
  selector: "h1, p", // Optional: CSS selector to extract specific content
  extractContents: true, // Extract text content only (true) or full HTML (false)
  timeout: 5000
})
```

### API Operation

Makes an API request with appropriate authentication and parameters:

```javascript
const result = await web({
  operation: "api",
  url: "https://api.example.com",
  method: "POST",
  endpoint: "/data",
  headers: {
    "Content-Type": "application/json"
  },
  body: JSON.stringify({ query: "example" }),
  apiKey: "your-api-key", // Optional: will use configured key if available
  responseType: "json"
})
```

### Search Operation

Performs a web search using the specified provider:

```javascript
const result = await web({
  operation: "search",
  provider: "google", // or "bing" or "duckduckgo"
  query: "Rust programming language",
  apiKey: "your-api-key", // Optional: will use configured key if available
  numResults: 5 // Optional: number of results to return
})
```

#### 1. Interactive Configuration During Authentication

When you run Alive AI for the first time or when you need to re-authenticate, you'll be prompted to configure web tool API keys as part of the authentication flow.

#### 2. Web Key Management Utility

Alive AI provides a dedicated utility to manage your web tool API keys:

```bash
npm run manage-web-keys
```

This interactive utility allows you to:
- View your current API key settings
- Update API keys for Google and Bing search
- Configure the Google Custom Search Engine ID
- Select your default search provider
- Clear all web tool API keys

#### 3. Command Line Arguments

You can specify API keys directly in the command line:

```bash
alive --enable-web-tool --google-api-key "YOUR_GOOGLE_API_KEY" --google-cx "YOUR_CUSTOM_SEARCH_ENGINE_ID" "Search for latest Python features"
```

```bash
alive --enable-web-tool --bing-api-key "YOUR_BING_API_KEY" "Search for latest JavaScript frameworks"
```

```bash
alive --enable-web-tool --search-provider duckduckgo "Search for programming tutorials"
```

#### 4. Environment Variables

You can set API keys using environment variables:

- `GOOGLE_API_KEY`: Google API key
- `GOOGLE_SEARCH_CX`: Google Custom Search Engine ID
- `BING_API_KEY`: Bing API key
- `ALIVE_DEFAULT_SEARCH_PROVIDER`: Default search provider (google, bing, or duckduckgo)

#### 5. Configuration File

You can save preferred configurations in your `~/.alive-ai/config.json` file:

```json
{
  "enableWebTool": true,
  "webTool": {
    "googleApiKey": "YOUR_GOOGLE_API_KEY",
    "googleSearchCx": "YOUR_CUSTOM_SEARCH_ENGINE_ID",
    "bingApiKey": "YOUR_BING_API_KEY",
    "defaultSearchProvider": "duckduckgo"
  }
}
```

## Available Operations

The web tool supports the following operations:

### 1. Fetch URL

Fetches raw content from a URL.

```javascript
web({
  operation: "fetch",
  url: "https://example.com",
  method: "GET", // Optional: GET, POST, PUT, DELETE, etc.
  headers: { "User-Agent": "AliveAI/1.0" }, // Optional
  responseType: "json" // Optional: json, text, arraybuffer, blob
})
```

### 2. Scrape Webpage

Scrapes HTML content from a webpage with optional selector filtering.

```javascript
web({
  operation: "scrape",
  url: "https://example.com",
  selector: "div.main-content", // Optional: CSS selector
  extractContents: true // Optional: true to get text only, false for HTML
})
```

### 3. API Request

Makes an API request with authentication and parameters.

```javascript
web({
  operation: "api",
  url: "https://api.example.com",
  endpoint: "/v1/data", // Optional
  method: "POST", // Optional
  headers: { "Authorization": "Bearer YOUR_API_KEY" }, // Optional
  body: JSON.stringify({ query: "data" }) // Optional
})
```

### 4. Web Search

Performs a web search using the configured provider.

```javascript
web({
  operation: "search",
  provider: "google", // google, bing, microsoft, yahoo, duckduckgo, stackoverflow, github
  query: "latest programming trends",
  numResults: 5 // Optional: number of results to return
})
```

## Limitations

- The web tool requires the `network-full-access` sandbox permission.
- Search results and fetched content may be truncated for very large responses.
- Some websites may block automated requests.
- API rate limits apply to all providers:
  - Google: 10 requests per minute
  - Bing/Microsoft: 10 requests per minute
  - DuckDuckGo: 20 requests per minute
  - Yahoo: 10 requests per minute
  - StackOverflow: 20 requests per minute
  - GitHub: 20 requests per minute
- DuckDuckGo, Yahoo, StackOverflow, and GitHub searches don't require API keys but may be less reliable than Google or Bing.
- Google search requires both an API key and a Custom Search Engine ID.
- Microsoft search uses the Bing API key.

## Rust Backend Implementation

The web tool is implemented in both the JavaScript CLI frontend and the Rust backend:

- The Rust backend implementation in `alive-rs/core/src/web_tool.rs` provides the core functionality.
- The web tool is integrated with the sandbox policy system to ensure security.
- Multiple search providers are supported with appropriate API key handling.
- All web operations are performed in a controlled environment with proper error handling.