import fs from "fs";
import os from "os";
import path from "path";
import { isValidPathString } from "./file-system-utils";

/**
 * Represents a file system suggestion with path and directory information
 */
export interface FileSystemSuggestion {
  /** The full path of the suggestion */
  path: string;
  /** Whether the suggestion is a directory */
  isDirectory: boolean;
}

/**
 * Gets file system suggestions based on a path prefix
 * @param pathPrefix The path prefix to search for
 * @param limit Maximum number of suggestions to return (default: 20)
 * @returns Array of file system suggestions
 */
export function getFileSystemSuggestions(
  pathPrefix: string,
  limit: number = 20
): Array<FileSystemSuggestion> {
  if (!pathPrefix || !isValidPathString(pathPrefix)) {
    return [];
  }

  try {
    const sep = path.sep;
    const hasTilde = pathPrefix === "~" || pathPrefix.startsWith("~" + sep);
    const expanded = hasTilde
      ? path.join(os.homedir(), pathPrefix.slice(1))
      : pathPrefix;

    const normalized = path.normalize(expanded);
    const isDir = pathPrefix.endsWith(path.sep);
    const base = path.basename(normalized);

    const dir =
      normalized === "." && !pathPrefix.startsWith("." + sep) && !hasTilde
        ? process.cwd()
        : path.dirname(normalized);

    const readDir = isDir ? path.join(dir, base) : dir;

    // Check if directory exists before trying to read it
    if (!fs.existsSync(readDir)) {
      return [];
    }

    // Get directory contents
    const dirContents = fs.readdirSync(readDir);

    // Filter and sort results
    const filteredItems = dirContents
      .filter((item) => isDir || item.toLowerCase().startsWith(base.toLowerCase())) // Case-insensitive matching
      .sort((a, b) => {
        // Sort directories first, then files
        const aPath = path.join(readDir, a);
        const bPath = path.join(readDir, b);
        const aIsDir = fs.statSync(aPath).isDirectory();
        const bIsDir = fs.statSync(bPath).isDirectory();

        if (aIsDir && !bIsDir) return -1;
        if (!aIsDir && bIsDir) return 1;

        // Then sort alphabetically
        return a.localeCompare(b);
      });

    // Map to FileSystemSuggestion objects and limit results
    return filteredItems
      .slice(0, limit)
      .map((item) => {
        const fullPath = path.join(readDir, item);
        let isDirectory = false;

        try {
          isDirectory = fs.statSync(fullPath).isDirectory();
        } catch (error) {
          // If we can't stat the file, assume it's not a directory
          isDirectory = false;
        }

        return {
          path: isDirectory ? path.join(fullPath, sep) : fullPath,
          isDirectory,
        };
      });
  } catch (error) {
    // Just return empty array on error
    return [];
  }
}
