#!/usr/bin/env node
import "dotenv/config";
import { CLI_DISPLAY_NAME } from "./constants";

// Exit early if on an older version of Node.js (< 22)
const major = process.versions.node.split(".").map(Number)[0]!;
if (major < 22) {
  // eslint-disable-next-line no-console
  console.error(
    "\n" +
      `${CLI_DISPLAY_NAME} CLI requires Node.js version 22 or newer.\n` +
      `You are running Node.js v${process.versions.node}.\n` +
      "Please upgrade Node.js: https://nodejs.org/en/download/\n",
  );
  process.exit(1);
}

// Hack to suppress deprecation warnings (punycode)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(process as any).noDeprecation = true;

import type { AppRollout } from "./app";
import type { ApprovalPolicy } from "./approvals";
import type { CommandConfirmation } from "./utils/agent/agent-loop";
import type { AppConfig } from "./utils/config";
import type { ResponseItem } from "openai/resources/responses/responses";
import type { ReasoningEffort } from "openai/resources.mjs";

import App from "./app";
import { runSinglePass } from "./cli-singlepass";
import SessionsOverlay from "./components/sessions-overlay.js";
import { analyzeCommand } from "./commands/analyze";
import { AgentLoop } from "./utils/agent/agent-loop";
import { ReviewDecision } from "./utils/agent/review";
import { AutoApprovalMode } from "./utils/auto-approval-mode";
import { checkForUpdates } from "./utils/check-updates";
import {
  loadConfig,
  PRETTY_PRINT,
  INSTRUCTIONS_FILEPATH,
} from "./utils/config";
import {
  getApiKey as fetchApiKey,
  maybeRedeemCredits,
} from "./utils/get-api-key";
import { getWebToolKeys } from "./utils/web-tool-keys";
import { createInputItem } from "./utils/input-utils";
import { initLogger } from "./utils/logger/log";
import { log } from "./utils/logger/log";
import { isModelSupportedForResponses } from "./utils/model-utils.js";
import { parseToolCall } from "./utils/parsers";
import { onExit, setInkRenderer } from "./utils/terminal";
import chalk from "chalk";
import { spawnSync, spawn } from "child_process";
import fs from "fs";
import { render } from "ink";
import meow from "meow";
import os from "os";
import path from "path";
import React from "react";
import ReactProvider from "./components/ReactProvider";
import { applyReactPatches, patchReactHooks } from "./utils/react-patch";

// Global error handler for React hook errors
// This prevents the application from crashing when hooks are used incorrectly
const originalConsoleError = console.error;
console.error = function(...args: any[]) {
  const errorMessage = args[0]?.toString() || '';

  // Check if this is a React hook error
  if (errorMessage.includes('Invalid hook call') ||
      errorMessage.includes('Hooks can only be called inside') ||
      errorMessage.includes('Cannot read properties of null') ||
      errorMessage.includes('object null is not iterable') ||
      errorMessage.includes('Symbol(Symbol.iterator)') ||
      errorMessage.includes('cursorOffset') ||
      errorMessage.includes('Cannot destructure property')) {
    // Log a simplified warning instead of crashing
    console.warn('React hook warning suppressed. This is expected during startup.');
    return;
  }

  // Pass through all other errors
  return originalConsoleError.apply(console, args);
};

// Create more robust mock implementations of React hooks to prevent crashes
const createSafeHook = (name: string, defaultValue: any) => {
  const originalHook = (React as any)[name];

  return function(...args: any[]) {
    try {
      // Only call the original hook if we're in a valid React component context
      if (typeof React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current !== 'undefined') {
        return originalHook.apply(React, args);
      } else {
        // If we're not in a component context, return the default value
        console.warn(`React hook warning: ${name} called outside component context. Using fallback.`);
        return defaultValue;
      }
    } catch (e) {
      // If it fails, return a default value
      console.warn(`React hook warning: ${name} failed. This is expected during startup.`);
      return defaultValue;
    }
  };
};

// Apply safe versions of commonly used hooks
React.useState = createSafeHook('useState', [null, () => {}]);
React.useEffect = createSafeHook('useEffect', undefined);
React.useRef = createSafeHook('useRef', { current: null });
React.useContext = createSafeHook('useContext', {});
React.useReducer = createSafeHook('useReducer', [null, () => {}]);
React.useCallback = createSafeHook('useCallback', () => {});
React.useMemo = createSafeHook('useMemo', null);

// Add additional protection for other hooks that might be used
React.useLayoutEffect = createSafeHook('useLayoutEffect', undefined);
React.useImperativeHandle = createSafeHook('useImperativeHandle', undefined);
React.useDebugValue = createSafeHook('useDebugValue', undefined);

// Patch specific React internals to prevent the cursorOffset error
// This addresses the specific error: "Cannot destructure property 'cursorOffset' of 'u' as it is null"
const originalCreateElement = React.createElement;
React.createElement = function(...args: any[]) {
  try {
    return originalCreateElement.apply(React, args);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Handle the specific cursorOffset error
    if (errorMessage.includes('cursorOffset') ||
        errorMessage.includes('Cannot destructure property')) {
      console.warn('React createElement error suppressed:', errorMessage);

      // Return a simple div element as fallback
      return originalCreateElement('div', {
        key: 'error-fallback',
        style: { padding: '10px', color: 'red' }
      }, 'Error rendering component');
    }

    // Re-throw other errors
    throw error;
  }
};

// Call this early so `tail -F "$TMPDIR/alive-ai/alive-cli-latest.log"` works
// immediately. This must be run with DEBUG=1 for logging to work.
initLogger();

// Apply React patches to handle specific errors
try {
  applyReactPatches();
  patchReactHooks(React);
  console.log("Applied React patches to improve stability");
} catch (error) {
  console.warn("Failed to apply React patches:", error);
}

// TODO: migrate to new versions of quiet mode
//
//     -q, --quiet    Non-interactive quiet mode that only prints final message
//     -j, --json     Non-interactive JSON output mode that prints JSON messages

const cli = meow(
  `
  Usage
    $ alive [options] <prompt>
    $ alive completion <bash|zsh|fish>
    $ alive analyze <file...>

  Commands
    analyze <file...>    Analyze code files for quality and suggestions

  Options
    --version                       Print version and exit

    -h, --help                      Show usage and exit
    -m, --model <model>             Model to use for completions (default: alive-mini-latest)
    -p, --provider <provider>       Provider to use for completions (default: openai)
    -i, --image <path>              Path(s) to image files to include as input
    -v, --view <rollout>            Inspect a previously saved rollout instead of starting a session
    --history                       Browse previous sessions
    --login                         Start a new sign in flow
    --free                          Retry redeeming free credits
    -q, --quiet                     Non-interactive mode that only prints the assistant's final output
    -c, --config                    Open the instructions file in your editor
    -w, --writable-root <path>      Writable folder for sandbox in full-auto mode (can be specified multiple times)
    -a, --approval-mode <mode>      Override the approval policy: 'suggest', 'auto-edit', or 'full-auto'

    --auto-edit                Automatically approve file edits; still prompt for commands
    --full-auto                Automatically approve edits and commands when executed in the sandbox

    --no-project-doc           Do not automatically include the repository's 'AGENTS.md'
    --project-doc <file>       Include an additional markdown file at <file> as context
    --full-stdout              Do not truncate stdout/stderr from command outputs
    --notify                   Enable desktop notifications for responses

    --disable-response-storage Disable server‑side response storage (sends the
                               full conversation context with every request)

    --flex-mode               Use "flex-mode" processing mode for the request (only supported
                              with models o3 and o4-mini)

    --reasoning <effort>      Set the reasoning effort level (low, medium, high) (default: high)

    --enable-web-tool         Enable the web tool for fetching data from the internet
                              (enabled by default)

    --google-api-key <key>    Set the Google API key for web search
    --bing-api-key <key>      Set the Bing API key for web search
    --google-cx <id>          Set the Google Custom Search Engine ID for search
    --search-provider <provider> Set the default search provider (google, bing, duckduckgo)

  Dangerous options
    --dangerously-auto-approve-everything
                               Skip all confirmation prompts and execute commands without
                               sandboxing. Intended solely for ephemeral local testing.

  Experimental options
    -f, --full-context         Launch in "full-context" mode which loads the entire repository
                               into context and applies a batch of edits in one go. Incompatible
                               with all other flags, except for --model.

  Available tools for the AI agent:
    - shell: Extended command execution with features like retry, background jobs, and custom shells
    - file: Enhanced file operations including copy, move, mkdir, stat, list and chmod
    - web: (If enabled) Safe access to web resources via fetch, scrape, and API calls

  Examples
    $ alive "Write and run a python program that prints ASCII art"
    $ alive -q "fix build issues"
    $ alive completion bash
`,
  {
    importMeta: import.meta,
    autoHelp: true,
    flags: {
      // misc
      help: { type: "boolean", aliases: ["h"] },
      version: { type: "boolean", description: "Print version and exit" },
      view: { type: "string" },
      history: { type: "boolean", description: "Browse previous sessions" },
      login: { type: "boolean", description: "Force a new sign in flow" },
      free: { type: "boolean", description: "Retry redeeming free credits" },
      model: { type: "string", aliases: ["m"] },
      provider: { type: "string", aliases: ["p"] },
      image: { type: "string", isMultiple: true, aliases: ["i"] },
      quiet: {
        type: "boolean",
        aliases: ["q"],
        description: "Non-interactive quiet mode",
      },
      config: {
        type: "boolean",
        aliases: ["c"],
        description: "Open the instructions file in your editor",
      },
      dangerouslyAutoApproveEverything: {
        type: "boolean",
        description:
          "Automatically approve all commands without prompting. This is EXTREMELY DANGEROUS and should only be used in trusted environments.",
      },
      autoEdit: {
        type: "boolean",
        description: "Automatically approve edits; prompt for commands.",
      },
      fullAuto: {
        type: "boolean",
        description:
          "Automatically run commands in a sandbox; only prompt for failures.",
      },
      approvalMode: {
        type: "string",
        aliases: ["a"],
        description:
          "Determine the approval mode for Alive AI (default: suggest) Values: suggest, auto-edit, full-auto",
      },
      writableRoot: {
        type: "string",
        isMultiple: true,
        aliases: ["w"],
        description:
          "Writable folder for sandbox in full-auto mode (can be specified multiple times)",
      },
      noProjectDoc: {
        type: "boolean",
        description: "Disable automatic inclusion of project-level AGENTS.md",
      },
      projectDoc: {
        type: "string",
        description: "Path to a markdown file to include as project doc",
      },
      flexMode: {
        type: "boolean",
        description:
          "Enable the flex-mode service tier (only supported by models o3 and o4-mini)",
      },
      fullStdout: {
        type: "boolean",
        description:
          "Disable truncation of command stdout/stderr messages (show everything)",
        aliases: ["no-truncate"],
      },
      reasoning: {
        type: "string",
        description: "Set the reasoning effort level (low, medium, high)",
        choices: ["low", "medium", "high"],
        default: "high",
      },
      disableResponseStorage: {
        type: "boolean",
        aliases: ["disable-response-storage"],
        description:
          "Disable server‑side response storage (send *everything* with each request)",
      },
      notify: {
        type: "boolean",
        description: "Enable desktop notifications for responses",
      },
      enableWebTool: {
        type: "boolean",
        aliases: ["enable-web-tool"],
        description: "Enable the web tool for fetching data from the internet",
      },
      googleApiKey: {
        type: "string",
        aliases: ["google-api-key"],
        description: "Set the Google API key for web search",
      },
      bingApiKey: {
        type: "string",
        aliases: ["bing-api-key"],
        description: "Set the Bing API key for web search",
      },
      googleCx: {
        type: "string",
        aliases: ["google-cx"],
        description: "Set the Google Custom Search Engine ID for search",
      },
      searchProvider: {
        type: "string",
        aliases: ["search-provider"],
        description: "Set the default search provider (google, bing, duckduckgo)",
        choices: ["google", "bing", "duckduckgo"],
      },
      // Experimental mode where whole directory is loaded in context and model is requested
      // to make code edits in a single pass.
      fullContext: {
        type: "boolean",
        aliases: ["f"],
        description: `Run in full-context editing approach. The model is given the whole code
          directory as context and performs changes in one go without acting.`,
      },
    },
  },
);

// ---------------------------------------------------------------------------
// Global flag handling
// ---------------------------------------------------------------------------

// Handle 'completion' subcommand before any prompting or API calls
if (cli.input[0] === "completion") {
  const shell = cli.input[1] || "bash";
  const scripts: Record<string, string> = {
    bash: `# bash completion for alive
_alive_completion() {
  local cur
  cur="\${COMP_WORDS[COMP_CWORD]}"
  COMPREPLY=( $(compgen -o default -o filenames -- "\${cur}") )
}
complete -F _alive_completion alive`,
    zsh: `# zsh completion for alive
#compdef alive

_alive() {
  _arguments '*:filename:_files'
}
_alive`,
    fish: `# fish completion for alive
complete -c alive -a '(__fish_complete_path)' -d 'file path'`,
  };
  const script = scripts[shell];
  if (!script) {
    // eslint-disable-next-line no-console
    console.error(`Unsupported shell: ${shell}`);
    process.exit(1);
  }
  // eslint-disable-next-line no-console
  console.log(script);
  process.exit(0);
}

// For --help, show help and exit.
if (cli.flags.help) {
  cli.showHelp();
}

// For --config, open custom instructions file in editor and exit.
if (cli.flags.config) {
  try {
    loadConfig(); // Ensures the file is created if it doesn't already exit.
  } catch {
    // ignore errors
  }

  const filePath = INSTRUCTIONS_FILEPATH;
  const editor =
    process.env["EDITOR"] || (process.platform === "win32" ? "notepad" : "vi");
  spawnSync(editor, [filePath], { stdio: "inherit" });
  process.exit(0);
}

// ---------------------------------------------------------------------------
// API key handling
// ---------------------------------------------------------------------------

const fullContextMode = Boolean(cli.flags.fullContext);
let config = loadConfig(undefined, undefined, {
  cwd: process.cwd(),
  disableProjectDoc: Boolean(cli.flags.noProjectDoc),
  projectDocPath: cli.flags.projectDoc,
  isFullContext: fullContextMode,
});

// `prompt` can be updated later when the user resumes a previous session
// via the `--history` flag. Therefore it must be declared with `let` rather
// than `const`.
let prompt = cli.input[0];
const model = cli.flags.model ?? config.model;
const imagePaths = cli.flags.image;
const provider = cli.flags.provider ?? config.provider ?? "openai";

const client = {
  issuer: "https://auth.openai.com",
  client_id: "app_EMoamEEZ73f0CkXaXp7hrann",
};

let apiKey = "";
let savedTokens:
  | {
      id_token?: string;
      access_token?: string;
      refresh_token: string;
    }
  | undefined;

// Try to load existing auth file if present
try {
  const home = os.homedir();
  const authDir = path.join(home, ".alive-ai");
  const authFile = path.join(authDir, "auth.json");
  if (fs.existsSync(authFile)) {
    const data = JSON.parse(fs.readFileSync(authFile, "utf-8"));
    savedTokens = data.tokens;
    const lastRefreshTime = data.last_refresh
      ? new Date(data.last_refresh).getTime()
      : 0;
    const expired = Date.now() - lastRefreshTime > 28 * 24 * 60 * 60 * 1000;
    if (data.OPENAI_API_KEY && !expired) {
      apiKey = data.OPENAI_API_KEY;
      log(`Loaded API key from auth.json file (last refresh: ${new Date(lastRefreshTime).toISOString()})`);
    } else if (expired) {
      log(`API key in auth.json file is expired (last refresh: ${new Date(lastRefreshTime).toISOString()})`);
    }

    // Load web tool configuration if available
    if (data.webTool) {
      log("Found web tool configuration in auth.json file");

      // Set environment variables if not already set
      if (data.webTool.googleApiKey && !process.env.GOOGLE_API_KEY) {
        process.env.GOOGLE_API_KEY = data.webTool.googleApiKey;
        log("Loaded Google API key from auth.json file");
      }

      if (data.webTool.googleSearchCx && !process.env.GOOGLE_SEARCH_CX) {
        process.env.GOOGLE_SEARCH_CX = data.webTool.googleSearchCx;
        log("Loaded Google Custom Search Engine ID from auth.json file");
      }

      if (data.webTool.bingApiKey && !process.env.BING_API_KEY) {
        process.env.BING_API_KEY = data.webTool.bingApiKey;
        log("Loaded Bing API key from auth.json file");
      }

      if (data.webTool.defaultSearchProvider && !process.env.ALIVE_DEFAULT_SEARCH_PROVIDER) {
        process.env.ALIVE_DEFAULT_SEARCH_PROVIDER = data.webTool.defaultSearchProvider;
        log(`Loaded default search provider (${data.webTool.defaultSearchProvider}) from auth.json file`);
      }

      // Update config with web tool settings
      config = {
        ...config,
        webTool: {
          ...config.webTool,
          googleApiKey: data.webTool.googleApiKey || config.webTool?.googleApiKey,
          bingApiKey: data.webTool.bingApiKey || config.webTool?.bingApiKey,
          googleSearchCx: data.webTool.googleSearchCx || config.webTool?.googleSearchCx,
          defaultSearchProvider: data.webTool.defaultSearchProvider || config.webTool?.defaultSearchProvider,
        }
      };
    }
  }
} catch (err) {
  log(`Error loading auth file: ${err}`);
  // ignore errors
}

// Check for API key in environment variable
if (!apiKey && process.env.OPENAI_API_KEY) {
  apiKey = process.env.OPENAI_API_KEY;
  log("Using API key from environment variable OPENAI_API_KEY");
}

if (cli.flags.login) {
  log("Forcing login due to --login flag");
  apiKey = await fetchApiKey(client.issuer, client.client_id, true);
  try {
    const home = os.homedir();
    const authDir = path.join(home, ".alive-ai");
    const authFile = path.join(authDir, "auth.json");
    if (fs.existsSync(authFile)) {
      const data = JSON.parse(fs.readFileSync(authFile, "utf-8"));
      savedTokens = data.tokens;
    }
  } catch (err) {
    log(`Error reading auth file after login: ${err}`);
    /* ignore */
  }
} else if (!apiKey) {
  log("No API key found, prompting user for login or API key");
  apiKey = await fetchApiKey(client.issuer, client.client_id);
}
// Ensure the API key is available as an environment variable for legacy code
process.env["OPENAI_API_KEY"] = apiKey;

if (cli.flags.free) {
  // eslint-disable-next-line no-console
  console.log(`${chalk.bold("alive --free")} attempting to redeem credits...`);
  if (!savedTokens?.refresh_token) {
    apiKey = await fetchApiKey(client.issuer, client.client_id, true);
    // fetchApiKey includes credit redemption as the end of the flow
  } else {
    await maybeRedeemCredits(
      client.issuer,
      client.client_id,
      savedTokens.refresh_token,
      savedTokens.id_token,
    );
  }
}

// Set of providers that don't require API keys
const NO_API_KEY_REQUIRED = new Set(["ollama"]);

// Check for provider-specific API key
const normalizedProvider = provider.toLowerCase();
const providerApiKey = process.env[`${provider.toUpperCase()}_API_KEY`];

// Skip API key validation for providers that don't require an API key
if (!apiKey && !providerApiKey && !NO_API_KEY_REQUIRED.has(normalizedProvider)) {
  // eslint-disable-next-line no-console
  console.error(
    `\n${chalk.red(`Missing ${provider} API key.`)}\n\n` +
      `Set the environment variable ${chalk.bold(
        `${provider.toUpperCase()}_API_KEY`,
      )} ` +
      `and re-run this command.\n` +
      `${
        normalizedProvider === "openai"
          ? `You can create a key here: ${chalk.bold(
              chalk.underline("https://platform.openai.com/account/api-keys"),
            )}\n`
          : normalizedProvider === "gemini"
            ? `You can create a ${chalk.bold(
                `${provider.toUpperCase()}_API_KEY`,
              )} ` + `in the ${chalk.bold(`Google AI Studio`)}.\n`
          : normalizedProvider === "deepseek"
            ? `You can create a ${chalk.bold(
                `${provider.toUpperCase()}_API_KEY`,
              )} ` + `in the ${chalk.bold(`DeepSeek AI`)} dashboard at ${chalk.bold(
                chalk.underline("https://platform.deepseek.com"),
              )}.\n`
            : `You can create a ${chalk.bold(
                `${provider.toUpperCase()}_API_KEY`,
              )} ` + `in the ${chalk.bold(`${provider}`)} dashboard.\n`
      }`,
  );
  process.exit(1);
}

// If we have a provider-specific API key, set it in the environment
if (providerApiKey) {
  process.env[`${provider.toUpperCase()}_API_KEY`] = providerApiKey;
}

const flagPresent = Object.hasOwn(cli.flags, "disableResponseStorage");

const disableResponseStorage = flagPresent
  ? Boolean(cli.flags.disableResponseStorage) // value user actually passed
  : (config.disableResponseStorage ?? false); // fall back to YAML, default to false

// Handle web tool configuration
if (cli.flags.enableWebTool) {
  // If the user has enabled the web tool but doesn't have API keys configured,
  // prompt them to configure the keys
  if (!config.webTool?.googleApiKey && !config.webTool?.bingApiKey &&
      !cli.flags.googleApiKey && !cli.flags.bingApiKey &&
      !process.env.GOOGLE_API_KEY && !process.env.BING_API_KEY) {
    // eslint-disable-next-line no-console
    console.log("Web tool is enabled. Would you like to configure API keys for search providers? (y/n)");

    // Use readline instead of React components for CLI configuration
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise<string>(resolve => {
      rl.question('', resolve);
    });

    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      // Configure Google API Key
      const googleApiKey = await new Promise<string>(resolve => {
        rl.question('Enter Google API Key (or press Enter to skip): ', resolve);
      });

      // Configure Google Search CX
      const googleSearchCx = await new Promise<string>(resolve => {
        rl.question('Enter Google Custom Search Engine ID (or press Enter to skip): ', resolve);
      });

      // Configure Bing API Key
      const bingApiKey = await new Promise<string>(resolve => {
        rl.question('Enter Bing API Key (or press Enter to skip): ', resolve);
      });

      // Configure Default Search Provider
      console.log('Select default search provider:');
      console.log('1. DuckDuckGo (no API key required)');
      console.log('2. Google (requires API key and CX)');
      console.log('3. Bing (requires API key)');

      const providerChoice = await new Promise<string>(resolve => {
        rl.question('Enter choice (1-3, default: 1): ', resolve);
      });

      let defaultSearchProvider: 'google' | 'bing' | 'duckduckgo' = 'duckduckgo';

      if (providerChoice === '2') {
        defaultSearchProvider = 'google';
      } else if (providerChoice === '3') {
        defaultSearchProvider = 'bing';
      }

      rl.close();

      // Update the config with the new keys
      config.webTool = {
        ...config.webTool,
        googleApiKey: googleApiKey || config.webTool?.googleApiKey,
        bingApiKey: bingApiKey || config.webTool?.bingApiKey,
        googleSearchCx: googleSearchCx || config.webTool?.googleSearchCx,
        defaultSearchProvider
      };

      // Also update environment variables for immediate use
      if (googleApiKey) {
        process.env.GOOGLE_API_KEY = googleApiKey;
      }
      if (bingApiKey) {
        process.env.BING_API_KEY = bingApiKey;
      }
      if (googleSearchCx) {
        process.env.GOOGLE_SEARCH_CX = googleSearchCx;
      }
      process.env.ALIVE_DEFAULT_SEARCH_PROVIDER = defaultSearchProvider;

      // Save the configuration
      try {
        const { updateWebToolConfig } = require('./utils/config');
        updateWebToolConfig({
          googleApiKey: googleApiKey || undefined,
          bingApiKey: bingApiKey || undefined,
          googleSearchCx: googleSearchCx || undefined,
          defaultSearchProvider
        });
        console.log('Web tool configuration saved successfully.');
      } catch (error) {
        console.error('Error saving web tool configuration:', error);
      }
    }
  }

  // If web tool flags are provided, update the config
  if (cli.flags.googleApiKey || cli.flags.bingApiKey || cli.flags.googleCx || cli.flags.searchProvider) {
    config.webTool = {
      ...config.webTool,
      googleApiKey: cli.flags.googleApiKey || config.webTool?.googleApiKey,
      bingApiKey: cli.flags.bingApiKey || config.webTool?.bingApiKey,
      googleSearchCx: cli.flags.googleCx || config.webTool?.googleSearchCx,
      defaultSearchProvider: cli.flags.searchProvider as any || config.webTool?.defaultSearchProvider,
    };

    // Update environment variables for immediate use
    if (cli.flags.googleApiKey) {
      process.env.GOOGLE_API_KEY = cli.flags.googleApiKey;
    }
    if (cli.flags.bingApiKey) {
      process.env.BING_API_KEY = cli.flags.bingApiKey;
    }
    if (cli.flags.googleCx) {
      process.env.GOOGLE_SEARCH_CX = cli.flags.googleCx;
    }
    if (cli.flags.searchProvider) {
      process.env.ALIVE_DEFAULT_SEARCH_PROVIDER = cli.flags.searchProvider;
    }
  }

  // Start the Web Tool MCP Server (enabled by default)
  try {
    // Get the path to our standalone web tool server launcher script
    const webToolLauncherScript = path.join(__dirname, 'scripts', 'launch-web-tool-server.js');

    // Check if the file exists
    if (fs.existsSync(webToolLauncherScript)) {
      log('Starting Web Tool MCP Server...');

      // Set environment variables for the web tool server
      process.env.GOOGLE_API_KEY = cli.flags.googleApiKey || config.webTool?.googleApiKey || process.env.GOOGLE_API_KEY || '';
      process.env.BING_API_KEY = cli.flags.bingApiKey || config.webTool?.bingApiKey || process.env.BING_API_KEY || '';
      process.env.GOOGLE_SEARCH_CX = cli.flags.googleCx || config.webTool?.googleSearchCx || process.env.GOOGLE_SEARCH_CX || '';
      process.env.ALIVE_DEFAULT_SEARCH_PROVIDER = cli.flags.searchProvider ||
        config.webTool?.defaultSearchProvider ||
        process.env.ALIVE_DEFAULT_SEARCH_PROVIDER ||
        'duckduckgo';

      // Start the server using our standalone script
      // This avoids React hook errors by running in a completely separate process
      const webToolServer = spawn('node', [webToolLauncherScript], {
        detached: true,
        stdio: 'ignore',
        env: process.env
      });

      // Detach the child process
      webToolServer.unref();

      // Register the MCP server in the config
      if (!config.mcpServers) {
        config.mcpServers = {};
      }

      // Add the web tool server to the MCP servers configuration
      config.mcpServers['web-tool'] = {
        command: 'node',
        args: [webToolLauncherScript],
        env: {
          GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
          BING_API_KEY: process.env.BING_API_KEY,
          GOOGLE_SEARCH_CX: process.env.GOOGLE_SEARCH_CX,
          ALIVE_DEFAULT_SEARCH_PROVIDER: process.env.ALIVE_DEFAULT_SEARCH_PROVIDER
        }
      };

      log('Web Tool MCP Server registered successfully');
    } else {
      log(`Web Tool Server launcher script not found at ${webToolLauncherScript}`);
    }
  } catch (error) {
    log(`Error starting Web Tool MCP Server: ${error}`);
  }
}

config = {
  apiKey,
  ...config,
  model: model ?? config.model,
  notify: Boolean(cli.flags.notify),
  reasoningEffort:
    (cli.flags.reasoning as ReasoningEffort | undefined) ?? "medium",
  flexMode: cli.flags.flexMode || (config.flexMode ?? false),
  provider,
  disableResponseStorage,
  enableWebTool: cli.flags.enableWebTool !== false && config.enableWebTool !== false,
};

// Check for updates after loading config. This is important because we write state file in
// the config dir.
try {
  await checkForUpdates();
} catch {
  // ignore
}

// For --flex-mode, validate and exit if incorrect.
if (config.flexMode) {
  const allowedFlexModels = new Set(["o3", "o4-mini"]);
  if (!allowedFlexModels.has(config.model)) {
    if (cli.flags.flexMode) {
      // eslint-disable-next-line no-console
      console.error(
        `The --flex-mode option is only supported when using the 'o3' or 'o4-mini' models. ` +
          `Current model: '${config.model}'.`,
      );
      process.exit(1);
    } else {
      config.flexMode = false;
    }
  }
}

// Check if the model is supported for the selected provider
if (!(await isModelSupportedForResponses(provider, config.model))) {
  // For DeepSeek, provide specific guidance
  if (normalizedProvider === "deepseek") {
    // eslint-disable-next-line no-console
    console.error(
      `The model "${config.model}" does not appear to be available for DeepSeek. ` +
        `Available DeepSeek models include:\n` +
        `  - deepseek-chat\n` +
        `  - deepseek-coder\n` +
        `  - deepseek-llm\n` +
        `  - deepseek-v2\n` +
        `  - alive-mini-latest\n` +
        `Please choose one of these models with the --model flag.`,
    );
  }
  // For OpenAI, provide OpenAI-specific guidance
  else if (normalizedProvider === "openai") {
    // eslint-disable-next-line no-console
    console.error(
      `The model "${config.model}" does not appear in the list of models ` +
        `available to your account. Double-check the spelling (use\n` +
        `  openai models list\n` +
        `to see the full list) or choose another model with the --model flag.`,
    );
  }
  // For other providers, provide generic guidance
  else {
    // eslint-disable-next-line no-console
    console.error(
      `The model "${config.model}" does not appear to be available for provider "${provider}". ` +
        `Please check the available models for this provider and choose a different model with the --model flag.`,
    );
  }
  process.exit(1);
}

let rollout: AppRollout | undefined;

// Handle analyze command
if (cli.input[0] === "analyze") {
  const filesToAnalyze = cli.input.slice(1);
  if (filesToAnalyze.length === 0) {
    // eslint-disable-next-line no-console
    console.error("Error: No files specified for analysis");
    process.exit(1);
  }

  await analyzeCommand(filesToAnalyze, { model: cli.flags.model });
  process.exit(0);
}

// For --history, show session selector and optionally update prompt or rollout.
if (cli.flags.history) {
  const result: { path: string; mode: "view" | "resume" } | null =
    await new Promise((resolve) => {
      const instance = render(
        <ReactProvider>
          {React.createElement(SessionsOverlay, {
            onView: (p: string) => {
              instance.unmount();
              resolve({ path: p, mode: "view" });
            },
            onResume: (p: string) => {
              instance.unmount();
              resolve({ path: p, mode: "resume" });
            },
            onExit: () => {
              instance.unmount();
              resolve(null);
            },
          })}
        </ReactProvider>
      );
    });

  if (!result) {
    process.exit(0);
  }

  if (result.mode === "view") {
    try {
      const content = fs.readFileSync(result.path, "utf-8");
      rollout = JSON.parse(content) as AppRollout;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error("Error reading session file:", error);
      process.exit(1);
    }
  } else {
    prompt = `Resume this session: ${result.path}`;
  }
}

// For --view, optionally load an existing rollout from disk, display it and exit.
if (cli.flags.view) {
  const viewPath = cli.flags.view;
  const absolutePath = path.isAbsolute(viewPath)
    ? viewPath
    : path.join(process.cwd(), viewPath);
  try {
    const content = fs.readFileSync(absolutePath, "utf-8");
    rollout = JSON.parse(content) as AppRollout;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error reading rollout file:", error);
    process.exit(1);
  }
}

// For --fullcontext, run the separate cli entrypoint and exit.
if (fullContextMode) {
  await runSinglePass({
    originalPrompt: prompt,
    config,
    rootPath: process.cwd(),
  });
  onExit();
  process.exit(0);
}

// Ensure that all values in additionalWritableRoots are absolute paths.
const additionalWritableRoots: ReadonlyArray<string> = (
  cli.flags.writableRoot ?? []
).map((p) => path.resolve(p));

// For --quiet, run the cli without user interactions and exit.
if (cli.flags.quiet) {
  process.env["ALIVE_QUIET_MODE"] = "1";
  if (!prompt || prompt.trim() === "") {
    // eslint-disable-next-line no-console
    console.error(
      'Quiet mode requires a prompt string, e.g.,: alive -q "Fix bug #123 in the foobar project"',
    );
    process.exit(1);
  }

  // Determine approval policy for quiet mode based on flags
  const quietApprovalPolicy: ApprovalPolicy =
    cli.flags.fullAuto || cli.flags.approvalMode === "full-auto"
      ? AutoApprovalMode.FULL_AUTO
      : cli.flags.autoEdit || cli.flags.approvalMode === "auto-edit"
        ? AutoApprovalMode.AUTO_EDIT
        : config.approvalMode || AutoApprovalMode.SUGGEST;

  // Update config with web tool settings for quiet mode
  const quietConfig: AppConfig = {
    ...config,
    enableWebTool: cli.flags.enableWebTool !== false && config.enableWebTool !== false,
    webTool: {
      googleApiKey: cli.flags.googleApiKey || config.webTool?.googleApiKey || process.env.GOOGLE_API_KEY || '',
      bingApiKey: cli.flags.bingApiKey || config.webTool?.bingApiKey || process.env.BING_API_KEY || '',
      googleSearchCx: cli.flags.googleCx || config.webTool?.googleSearchCx || process.env.GOOGLE_SEARCH_CX || '',
      defaultSearchProvider: (cli.flags.searchProvider ||
        config.webTool?.defaultSearchProvider ||
        process.env.ALIVE_DEFAULT_SEARCH_PROVIDER ||
        'duckduckgo') as 'google' | 'bing' | 'microsoft' | 'yahoo' | 'duckduckgo' | 'stackoverflow' | 'github'
    }
  };

  await runQuietMode({
    prompt,
    imagePaths: imagePaths || [],
    approvalPolicy: quietApprovalPolicy,
    additionalWritableRoots,
    config: quietConfig,
  });
  onExit();
  process.exit(0);
}

// Default to the "suggest" policy.
// Determine the approval policy to use in interactive mode.
//
// Priority (highest → lowest):
// 1. --fullAuto – run everything automatically in a sandbox.
// 2. --dangerouslyAutoApproveEverything – run everything **without** a sandbox
//    or prompts.  This is intended for completely trusted environments.  Since
//    it is more dangerous than --fullAuto we deliberately give it lower
//    priority so a user specifying both flags still gets the safer behaviour.
// 3. --autoEdit – automatically approve edits, but prompt for commands.
// 4. config.approvalMode - use the approvalMode setting from ~/.alive-ai/config.json.
// 5. Default – suggest mode (prompt for everything).

const approvalPolicy: ApprovalPolicy =
  cli.flags.fullAuto || cli.flags.approvalMode === "full-auto"
    ? AutoApprovalMode.FULL_AUTO
    : cli.flags.autoEdit || cli.flags.approvalMode === "auto-edit"
      ? AutoApprovalMode.AUTO_EDIT
      : config.approvalMode || AutoApprovalMode.SUGGEST;

// Create a fallback component in case the main App fails to render
// Using a class component to avoid React hook issues
class FallbackComponent extends React.Component {
  // Add a timer to automatically exit after 5 seconds
  componentDidMount() {
    this.timer = setTimeout(() => {
      // We can't use useApp().exit() here, so we'll just exit the process
      process.exit(0);
    }, 5000);
  }

  componentWillUnmount() {
    // Clean up the timer
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }

  render() {
    return React.createElement('div', { style: { padding: '10px' } }, [
      React.createElement('h1', null, 'Alive AI CLI'),
      React.createElement('p', null, 'There was an error initializing the interactive UI.'),
      React.createElement('p', null, 'Try using the quiet mode with: alive -q "your prompt"'),
      React.createElement('p', { style: { color: 'gray' } }, '(This message will close automatically in 5 seconds)')
    ]);
  }
}

// Wrap the App component in an error boundary
let appComponent;
try {
  appComponent = React.createElement(App, {
    prompt: prompt,
    config: {
      ...config,
      disableResponseStorage: disableResponseStorage,
      flexMode: config.flexMode,
      reasoningEffort: config.reasoningEffort,
      enableWebTool: cli.flags.enableWebTool !== false && config.enableWebTool !== false,
      webTool: {
        googleApiKey: cli.flags.googleApiKey || config.webTool?.googleApiKey || process.env.GOOGLE_API_KEY || '',
        bingApiKey: cli.flags.bingApiKey || config.webTool?.bingApiKey || process.env.BING_API_KEY || '',
        googleSearchCx: cli.flags.googleCx || config.webTool?.googleSearchCx || process.env.GOOGLE_SEARCH_CX || '',
        defaultSearchProvider: (cli.flags.searchProvider ||
          config.webTool?.defaultSearchProvider ||
          process.env.ALIVE_DEFAULT_SEARCH_PROVIDER ||
          'duckduckgo') as 'google' | 'bing' | 'microsoft' | 'yahoo' | 'duckduckgo' | 'stackoverflow' | 'github'
      }
    },
    imagePaths: imagePaths,
    rollout: rollout,
    approvalPolicy: approvalPolicy,
    additionalWritableRoots: additionalWritableRoots,
    fullStdout: Boolean(cli.flags.fullStdout)
  });
} catch (error) {
  console.error('Error creating App component:', error);
  appComponent = React.createElement(FallbackComponent);
}

// Create a simple fallback component that doesn't use any hooks
class SimpleFallbackComponent extends React.Component {
  render() {
    return React.createElement('div', null,
      React.createElement('div', null, 'Alive AI encountered an error during startup.'),
      React.createElement('div', null, 'Please try using quiet mode with: alive -q "your prompt"')
    );
  }
}

// Render with enhanced error handling
let instance;
try {
  // Wrap the entire rendering in a try-catch to prevent crashes
  const wrappedComponent = React.createElement(
    ({ children }) => {
      try {
        return children;
      } catch (error) {
        console.error('Error in component render:', error);
        return React.createElement(SimpleFallbackComponent);
      }
    },
    null,
    appComponent
  );

  // Monkey patch the react-reconciler to handle the cursorOffset error
  // This is a temporary fix until the underlying issue is resolved
  try {
    // Find the react-reconciler module in node_modules
    const reactReconcilerPath = require.resolve('react-reconciler');
    const reactReconciler = require(reactReconcilerPath);

    // If we can access the internals, patch the function that causes the error
    if (reactReconciler.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      const internals = reactReconciler.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;

      // Look for functions that might be causing the cursorOffset error
      Object.keys(internals).forEach(key => {
        const original = internals[key];
        if (typeof original === 'function') {
          internals[key] = function(...args) {
            try {
              return original.apply(this, args);
            } catch (error) {
              if (String(error).includes('cursorOffset') ||
                  String(error).includes('Cannot destructure property')) {
                console.warn(`Suppressed error in react-reconciler ${key}:`, String(error));
                return null;
              }
              throw error;
            }
          };
        }
      });
    }
  } catch (patchError) {
    console.warn('Failed to patch react-reconciler:', patchError);
  }

  instance = render(
    React.createElement(ReactProvider, null, wrappedComponent),
    {
      patchConsole: process.env["DEBUG"] ? false : true,
    }
  );
} catch (error) {
  console.error('Error rendering App:', error);

  // Try to render just the fallback component
  try {
    instance = render(
      React.createElement(ReactProvider, null, React.createElement(SimpleFallbackComponent)),
      {
        patchConsole: process.env["DEBUG"] ? false : true,
      }
    );
  } catch (innerError) {
    console.error('Fatal error: Could not render even the simple fallback component:', innerError);
    console.log('Falling back to quiet mode...');

    // If all rendering fails, try to run in quiet mode with a default message
    if (prompt) {
      try {
        await runQuietMode({
          prompt: prompt,
          imagePaths: imagePaths || [],
          approvalPolicy,
          additionalWritableRoots,
          config,
        });
        process.exit(0);
      } catch (quietModeError) {
        console.error('Error running in quiet mode:', quietModeError);
        process.exit(1);
      }
    } else {
      console.log('Please try using the quiet mode with: alive -q "your prompt"');
      process.exit(1);
    }
  }
}

// Set the Ink renderer with error handling
try {
  if (instance) {
    setInkRenderer(instance);
  }
} catch (error) {
  console.error('Error setting Ink renderer:', error);
}

function formatResponseItemForQuietMode(item: ResponseItem): string {
  if (!PRETTY_PRINT) {
    return JSON.stringify(item);
  }
  switch (item.type) {
    case "message": {
      const role = item.role === "assistant" ? "assistant" : item.role;
      const txt = item.content
        .map((c) => {
          if (c.type === "output_text" || c.type === "input_text") {
            return c.text;
          }
          if (c.type === "input_image") {
            return "<Image>";
          }
          if (c.type === "input_file") {
            return c.filename;
          }
          if (c.type === "refusal") {
            return c.refusal;
          }
          return "?";
        })
        .join(" ");
      return `${role}: ${txt}`;
    }
    case "function_call": {
      const details = parseToolCall(item);
      return `$ ${details?.cmdReadableText ?? item.name}`;
    }
    case "function_call_output": {
      // @ts-expect-error metadata unknown on ResponseFunctionToolCallOutputItem
      const meta = item.metadata as ExecOutputMetadata;
      const parts: Array<string> = [];
      if (typeof meta?.exit_code === "number") {
        parts.push(`code: ${meta.exit_code}`);
      }
      if (typeof meta?.duration_seconds === "number") {
        parts.push(`duration: ${meta.duration_seconds}s`);
      }
      const header = parts.length > 0 ? ` (${parts.join(", ")})` : "";
      return `command.stdout${header}\n${item.output}`;
    }
    default: {
      return JSON.stringify(item);
    }
  }
}

async function runQuietMode({
  prompt,
  imagePaths,
  approvalPolicy,
  additionalWritableRoots,
  config,
}: {
  prompt: string;
  imagePaths: Array<string>;
  approvalPolicy: ApprovalPolicy;
  additionalWritableRoots: ReadonlyArray<string>;
  config: AppConfig;
}): Promise<void> {
  const agent = new AgentLoop({
    model: config.model,
    config: config,
    instructions: config.instructions,
    provider: config.provider,
    approvalPolicy,
    additionalWritableRoots,
    disableResponseStorage: config.disableResponseStorage,
    onItem: (item: ResponseItem) => {
      // eslint-disable-next-line no-console
      console.log(formatResponseItemForQuietMode(item));
    },
    onLoading: () => {
      /* intentionally ignored in quiet mode */
    },
    getCommandConfirmation: (
      _command: Array<string>,
    ): Promise<CommandConfirmation> => {
      // In quiet mode, default to NO_CONTINUE, except when in full-auto mode
      const reviewDecision =
        approvalPolicy === AutoApprovalMode.FULL_AUTO
          ? ReviewDecision.YES
          : ReviewDecision.NO_CONTINUE;
      return Promise.resolve({ review: reviewDecision });
    },
    onLastResponseId: () => {
      /* intentionally ignored in quiet mode */
    },
  });

  const inputItem = await createInputItem(prompt, imagePaths);
  await agent.run([inputItem]);
}

const exit = () => {
  onExit();
  process.exit(0);
};

process.on("SIGINT", exit);
process.on("SIGQUIT", exit);
process.on("SIGTERM", exit);

// ---------------------------------------------------------------------------
// Fallback for Ctrl-C when stdin is in raw-mode
// ---------------------------------------------------------------------------

if (process.stdin.isTTY) {
  // Ensure we do not leave the terminal in raw mode if the user presses
  // Ctrl-C while some other component has focus and Ink is intercepting
  // input. Node does *not* emit a SIGINT in raw-mode, so we listen for the
  // corresponding byte (0x03) ourselves and trigger a graceful shutdown.
  const onRawData = (data: Buffer | string): void => {
    const str = Buffer.isBuffer(data) ? data.toString("utf8") : data;
    if (str === "\u0003") {
      exit();
    }
  };
  process.stdin.on("data", onRawData);


}

// Ensure terminal clean-up always runs, even when other code calls
// `process.exit()` directly.
process.once("exit", onExit);
