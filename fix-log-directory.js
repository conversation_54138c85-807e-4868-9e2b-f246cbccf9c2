/**
 * Fix Log Directory Script
 * 
 * This script creates the necessary log directory structure for Alive AI.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Create the log directory
const logDir = path.join(os.tmpdir(), 'alive-ai');

console.log(`Creating log directory: ${logDir}`);

// Create the directory if it doesn't exist
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
  console.log('Log directory created successfully');
} else {
  console.log('Log directory already exists');
}

// Verify the directory exists and is writable
try {
  const testFile = path.join(logDir, 'test.log');
  fs.writeFileSync(testFile, 'Test log file');
  fs.unlinkSync(testFile);
  console.log('Log directory is writable');
} catch (error) {
  console.error(`Error writing to log directory: ${error.message}`);
  process.exit(1);
}

console.log('Log directory setup complete');
