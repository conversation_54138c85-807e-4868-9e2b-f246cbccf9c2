/**
 * Web Tool MCP Server
 *
 * This file implements a Model Context Protocol (MCP) server for web operations
 * including fetching URLs, scraping webpages, making API requests, and performing searches.
 */

import { fetchUrl, scrapeWebpage, makeApiRequest, performSearch } from '../utils/web-tool';
import { WebToolKeys } from '../utils/web-tool-keys';

// MCP Protocol constants
const MCP_SCHEMA_VERSION = '0.1.0';
const JSONRPC_VERSION = '2.0';

// Tool definitions
const WEB_TOOLS = {
  'fetch': {
    name: 'fetch',
    description: 'Fetches content from a URL with specified options',
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The URL to fetch'
        },
        method: {
          type: 'string',
          description: 'HTTP method (GET, POST, PUT, DELETE, etc.)',
          default: 'GET'
        },
        body: {
          type: 'string',
          description: 'Request body for POST, PUT, etc.'
        },
        headers: {
          type: 'object',
          description: 'HTTP headers to include in the request'
        },
        responseType: {
          type: 'string',
          description: 'Response type (json, text, arraybuffer, blob)',
          enum: ['json', 'text', 'arraybuffer', 'blob'],
          default: 'text'
        },
        timeout: {
          type: 'number',
          description: 'Request timeout in milliseconds'
        },
        followRedirects: {
          type: 'boolean',
          description: 'Whether to follow redirects',
          default: true
        },
        maxRedirects: {
          type: 'number',
          description: 'Maximum number of redirects to follow',
          default: 5
        },
        cacheResults: {
          type: 'boolean',
          description: 'Whether to cache results',
          default: true
        }
      },
      required: ['url']
    }
  },
  'scrape': {
    name: 'scrape',
    description: 'Scrapes content from a webpage with optional selector filtering',
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The URL to scrape'
        },
        selector: {
          type: 'string',
          description: 'CSS selector to extract specific content'
        },
        extractContents: {
          type: 'boolean',
          description: 'Extract text content only (true) or full HTML (false)',
          default: true
        },
        timeout: {
          type: 'number',
          description: 'Request timeout in milliseconds'
        },
        headers: {
          type: 'object',
          description: 'HTTP headers to include in the request'
        },
        cacheResults: {
          type: 'boolean',
          description: 'Whether to cache results',
          default: true
        }
      },
      required: ['url']
    }
  },
  'api': {
    name: 'api',
    description: 'Makes an API request with appropriate authentication and parameters',
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The base URL for the API'
        },
        method: {
          type: 'string',
          description: 'HTTP method (GET, POST, PUT, DELETE, etc.)',
          default: 'GET'
        },
        endpoint: {
          type: 'string',
          description: 'API endpoint to append to the base URL'
        },
        body: {
          type: 'string',
          description: 'Request body for POST, PUT, etc.'
        },
        headers: {
          type: 'object',
          description: 'HTTP headers to include in the request'
        },
        apiKey: {
          type: 'string',
          description: 'API key for authentication'
        },
        responseType: {
          type: 'string',
          description: 'Response type (json, text, arraybuffer, blob)',
          enum: ['json', 'text', 'arraybuffer', 'blob'],
          default: 'json'
        },
        timeout: {
          type: 'number',
          description: 'Request timeout in milliseconds'
        },
        cacheResults: {
          type: 'boolean',
          description: 'Whether to cache results',
          default: true
        }
      },
      required: ['url']
    }
  },
  'search': {
    name: 'search',
    description: 'Performs a web search using the specified provider',
    parameters: {
      type: 'object',
      properties: {
        provider: {
          type: 'string',
          description: 'Search provider (google, bing, microsoft, yahoo, duckduckgo, stackoverflow, github)',
          enum: ['google', 'bing', 'microsoft', 'yahoo', 'duckduckgo', 'stackoverflow', 'github'],
          default: 'duckduckgo'
        },
        query: {
          type: 'string',
          description: 'Search query'
        },
        apiKey: {
          type: 'string',
          description: 'API key for the search provider (required for Google and Bing)'
        },
        cx: {
          type: 'string',
          description: 'Google Custom Search Engine ID (required for Google)'
        },
        numResults: {
          type: 'number',
          description: 'Number of results to return',
          default: 5
        },
        cacheResults: {
          type: 'boolean',
          description: 'Whether to cache results',
          default: true
        },
        filters: {
          type: 'object',
          description: 'Search filters',
          properties: {
            timeRange: {
              type: 'string',
              description: 'Time range for results',
              enum: ['day', 'week', 'month', 'year', 'all']
            },
            siteRestriction: {
              type: 'string',
              description: 'Restrict search to specific site (e.g., "example.com")'
            },
            fileType: {
              type: 'string',
              description: 'Restrict to specific file types (e.g., "pdf", "doc")'
            },
            language: {
              type: 'string',
              description: 'Language code (e.g., "en", "es")'
            }
          }
        }
      },
      required: ['query']
    }
  }
};

// MCP message types
type McpRequest = {
  jsonrpc: string;
  id: string | number;
  method: string;
  params?: any;
};

type McpResponse = {
  jsonrpc: string;
  id: string | number;
  result: any;
};

type McpError = {
  jsonrpc: string;
  id: string | number;
  error: {
    code: number;
    message: string;
    data?: any;
  };
};

type McpNotification = {
  jsonrpc: string;
  method: string;
  params?: any;
};

/**
 * Handles MCP requests and returns responses
 */
class WebToolMcpServer {
  private webToolKeys: WebToolKeys;

  constructor(webToolKeys: WebToolKeys) {
    this.webToolKeys = webToolKeys;
  }

  /**
   * Processes an MCP message and returns a response
   */
  async processMessage(message: string): Promise<string | null> {
    try {
      const parsedMessage = JSON.parse(message);

      // Handle batch requests
      if (Array.isArray(parsedMessage)) {
        const responses = await Promise.all(
          parsedMessage.map(msg => this.handleSingleMessage(msg))
        );
        return JSON.stringify(responses.filter(r => r !== null));
      }

      // Handle single request
      const response = await this.handleSingleMessage(parsedMessage);
      return response ? JSON.stringify(response) : null;
    } catch (error) {
      // If we can't parse the message, return a parse error
      return JSON.stringify({
        jsonrpc: JSONRPC_VERSION,
        id: null,
        error: {
          code: -32700,
          message: 'Parse error',
          data: error instanceof Error ? error.message : String(error)
        }
      });
    }
  }

  /**
   * Handles a single MCP message
   */
  private async handleSingleMessage(message: any): Promise<McpResponse | McpError | null> {
    // Check if it's a notification (no id)
    if (!message.id) {
      await this.handleNotification(message);
      return null;
    }

    // Handle request
    try {
      switch (message.method) {
        case 'initialize':
          return this.handleInitialize(message);
        case 'tools/list':
          return this.handleToolsList(message);
        case 'tools/call':
          return await this.handleToolsCall(message);
        default:
          return {
            jsonrpc: JSONRPC_VERSION,
            id: message.id,
            error: {
              code: -32601,
              message: `Method not found: ${message.method}`
            }
          };
      }
    } catch (error) {
      return {
        jsonrpc: JSONRPC_VERSION,
        id: message.id,
        error: {
          code: -32603,
          message: 'Internal error',
          data: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * Handles MCP notifications
   */
  private async handleNotification(_notification: McpNotification): Promise<void> {
    // Handle notifications if needed in the future
    // Currently just a placeholder for protocol compliance
  }

  /**
   * Handles the initialize request
   */
  private handleInitialize(request: McpRequest): McpResponse {
    return {
      jsonrpc: JSONRPC_VERSION,
      id: request.id,
      result: {
        capabilities: {
          tools: {
            list_changed: true
          }
        },
        protocol_version: MCP_SCHEMA_VERSION,
        server_info: {
          name: 'alive-ai-web-tool-server',
          version: MCP_SCHEMA_VERSION
        }
      }
    };
  }

  /**
   * Handles the tools/list request
   */
  private handleToolsList(request: McpRequest): McpResponse {
    return {
      jsonrpc: JSONRPC_VERSION,
      id: request.id,
      result: {
        tools: Object.values(WEB_TOOLS)
      }
    };
  }

  /**
   * Handles the tools/call request
   */
  private async handleToolsCall(request: McpRequest): Promise<McpResponse | McpError> {
    const { tool, arguments: args } = request.params || {};

    if (!tool) {
      return {
        jsonrpc: JSONRPC_VERSION,
        id: request.id,
        error: {
          code: -32602,
          message: 'Invalid params: tool name is required'
        }
      };
    }

    let parsedArgs: any = {};
    try {
      parsedArgs = args ? JSON.parse(args) : {};
    } catch (error) {
      return {
        jsonrpc: JSONRPC_VERSION,
        id: request.id,
        error: {
          code: -32602,
          message: 'Invalid params: arguments must be valid JSON'
        }
      };
    }

    try {
      let result: any;

      switch (tool) {
        case 'fetch':
          result = await this.handleFetchTool(parsedArgs);
          break;
        case 'scrape':
          result = await this.handleScrapeTool(parsedArgs);
          break;
        case 'api':
          result = await this.handleApiTool(parsedArgs);
          break;
        case 'search':
          result = await this.handleSearchTool(parsedArgs);
          break;
        default:
          return {
            jsonrpc: JSONRPC_VERSION,
            id: request.id,
            error: {
              code: -32601,
              message: `Tool not found: ${tool}`
            }
          };
      }

      return {
        jsonrpc: JSONRPC_VERSION,
        id: request.id,
        result: {
          content: {
            kind: 'text',
            value: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
          }
        }
      };
    } catch (error) {
      return {
        jsonrpc: JSONRPC_VERSION,
        id: request.id,
        error: {
          code: -32603,
          message: 'Tool execution failed',
          data: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * Handles the fetch tool
   */
  private async handleFetchTool(args: any): Promise<any> {
    const { url, method = 'GET', body, ...options } = args;
    const result = await fetchUrl(url, method, body, options);
    return result;
  }

  /**
   * Handles the scrape tool
   */
  private async handleScrapeTool(args: any): Promise<any> {
    const { url, ...options } = args;
    const result = await scrapeWebpage(url, options);
    return result;
  }

  /**
   * Handles the API tool
   */
  private async handleApiTool(args: any): Promise<any> {
    const { url, method = 'GET', body, ...options } = args;
    const result = await makeApiRequest(url, method, body, options);
    return result;
  }

  /**
   * Handles the search tool
   */
  private async handleSearchTool(args: any): Promise<any> {
    // Add API keys if not provided
    const options = { ...args };

    if (options.provider === 'google' && !options.apiKey) {
      options.apiKey = this.webToolKeys.googleApiKey;
      options.cx = options.cx || this.webToolKeys.googleSearchCx;
    } else if (options.provider === 'bing' && !options.apiKey) {
      options.apiKey = this.webToolKeys.bingApiKey;
    }

    const result = await performSearch(options);
    return result;
  }
}

/**
 * Starts the Web Tool MCP Server
 */
export function startWebToolServer(webToolKeys: WebToolKeys): void {
  console.log('Starting Web Tool MCP Server...');

  const server = new WebToolMcpServer(webToolKeys);

  // Set up stdin/stdout communication
  process.stdin.setEncoding('utf8');

  let buffer = '';

  // Process incoming messages
  process.stdin.on('data', async (chunk) => {
    buffer += chunk;

    // Process complete messages (separated by newlines)
    const lines = buffer.split('\n');
    buffer = lines.pop() || ''; // Keep the last incomplete line in the buffer

    for (const line of lines) {
      if (line.trim()) {
        try {
          const response = await server.processMessage(line);
          if (response) {
            process.stdout.write(response + '\n');
          }
        } catch (error) {
          console.error('Error processing message:', error);
        }
      }
    }
  });

  // Handle process exit
  process.on('SIGTERM', () => {
    console.log('Web Tool MCP Server shutting down...');
    process.exit(0);
  });

  console.log('Web Tool MCP Server started and ready to process requests');
}
