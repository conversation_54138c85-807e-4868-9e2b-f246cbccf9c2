#!/usr/bin/env node
/**
 * Alive AI Fallback Script
 *
 * This script attempts to run the Node.js version of Alive AI.
 * If it fails with the cursorOffset error, it automatically falls back to the Rust version.
 */

import { spawn, spawnSync } from 'child_process';
import path from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import fs from 'fs';

// Determine this script's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Resolve the path to the compiled CLI bundle
const cliPath = path.resolve(__dirname, "../dist/cli.js");
const cliUrl = pathToFileURL(cliPath).href;

// Log the paths for debugging
console.log('CLI Path:', cliPath);
console.log('Current directory:', process.cwd());

// Check if the Rust version is available
function isRustVersionAvailable() {
  try {
    // Check if the alive-rs directory exists
    const rustDirPath = path.resolve(__dirname, '../../alive-rs');
    if (!fs.existsSync(rustDirPath)) {
      return false;
    }

    // Try to run cargo to check if Rust is installed
    const result = spawnSync('cargo', ['--version'], {
      stdio: 'ignore',
      shell: true
    });

    return result.status === 0;
  } catch (error) {
    return false;
  }
}

// Run the Rust version of Alive AI
function runRustVersion() {
  console.log('Falling back to Rust version of Alive AI...');

  // Change to the alive-rs directory
  process.chdir(path.resolve(__dirname, '../../alive-rs'));

  // Run the Rust version
  const rustProcess = spawn('cargo', ['run', '--bin', 'alive', ...process.argv.slice(2)], {
    stdio: 'inherit',
    shell: true
  });

  // Handle process exit
  rustProcess.on('exit', (code) => {
    process.exit(code || 0);
  });

  // Handle process errors
  rustProcess.on('error', (error) => {
    console.error('Error running Rust version:', error);
    process.exit(1);
  });
}

// Run the Node.js version with error detection
async function runNodeVersion() {
  try {
    // Create a child process to run the Node.js version
    const nodeProcess = spawn('node', [cliPath, ...process.argv.slice(2)], {
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });

    let errorOutput = '';
    let hasError = false;

    // Collect stderr output
    nodeProcess.stderr.on('data', (data) => {
      const chunk = data.toString();
      errorOutput += chunk;
      process.stderr.write(data);

      // Check for the specific error
      if (chunk.includes('cursorOffset') ||
          chunk.includes('Cannot destructure property')) {
        hasError = true;

        // Kill the Node.js process
        nodeProcess.kill();

        // Check if Rust version is available
        if (isRustVersionAvailable()) {
          runRustVersion();
        } else {
          console.error('\nError: Node.js version failed and Rust version is not available.');
          process.exit(1);
        }
      }
    });

    // Pass through stdout
    nodeProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });

    // Handle process exit
    nodeProcess.on('exit', (code) => {
      if (!hasError) {
        process.exit(code || 0);
      }
    });

    // Handle process errors
    nodeProcess.on('error', (error) => {
      console.error('Error running Node.js version:', error);

      // Check if Rust version is available
      if (isRustVersionAvailable()) {
        runRustVersion();
      } else {
        process.exit(1);
      }
    });
  } catch (error) {
    console.error('Error launching Node.js version:', error);

    // Check if Rust version is available
    if (isRustVersionAvailable()) {
      runRustVersion();
    } else {
      process.exit(1);
    }
  }
}

// Start by running the Node.js version
runNodeVersion();
