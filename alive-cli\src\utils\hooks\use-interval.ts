import { useEffect, useRef } from 'react';

/**
 * Custom useInterval hook that calls a callback function at a specified interval
 * 
 * @param callback The function to call at each interval
 * @param delay The delay in milliseconds between each call (or null to pause)
 */
export function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = useRef<() => void>();

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval
  useEffect(() => {
    function tick() {
      savedCallback.current?.();
    }

    if (delay !== null) {
      const id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
    
    return undefined;
  }, [delay]);
}
