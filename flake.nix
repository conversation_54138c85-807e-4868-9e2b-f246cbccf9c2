{
  description = "Development Nix flake for Alive AI CLI";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
    rust-overlay = {
      url = "github:oxalica/rust-overlay";
      inputs.nixpkgs.follows = "nixpkgs";
    };
  };

  outputs = { nixpkgs, flake-utils, rust-overlay, ... }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = import nixpkgs {
          inherit system;
        };
        pkgsWithRust = import nixpkgs {
          inherit system;
          overlays = [ rust-overlay.overlays.default ];
        };
        monorepo-deps = with pkgs; [
          # for precommit hook
          pnpm
          husky
        ];
        alive-cli = import ./alive-cli {
          inherit pkgs monorepo-deps;
        };
        alive-rs = import ./alive-rs {
          pkgs = pkgsWithRust;
          inherit monorepo-deps;
        };
      in
      rec {
        packages = {
          alive-cli = alive-cli.package;
          alive-rs = alive-rs.package;
        };

        devShells = {
          alive-cli = alive-cli.devShell;
          alive-rs = alive-rs.devShell;
        };

        apps = {
          alive-cli = alive-cli.app;
          alive-rs = alive-rs.app;
        };

        defaultPackage = packages.alive-cli;
        defaultApp = apps.alive-cli;
        defaultDevShell = devShells.alive-cli;
      }
    );
}
