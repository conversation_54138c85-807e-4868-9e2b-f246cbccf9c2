import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { fetchUrl, scrapeWebpage, makeApiRequest, performSearch } from './web-tool';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

// Mock axios
vi.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock fs functions
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
}));

describe('Web Tool', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock cache directory existence
    (fs.existsSync as jest.Mock).mockImplementation((path: string) => {
      if (path.includes('cache')) {
        return true;
      }
      return false;
    });

    // Mock cache file reading
    (fs.readFileSync as jest.Mock).mockImplementation((path: string, encoding: string) => {
      if (path.includes('cache')) {
        return JSON.stringify({
          timestamp: Date.now() - 1000, // 1 second ago
          response: {
            output: 'Cached content',
            success: true,
            metadata: {
              source: 'cache',
              timestamp: Date.now() - 1000
            }
          }
        });
      }
      throw new Error('File not found');
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('fetchUrl', () => {
    it('should fetch content from a URL', async () => {
      // Mock successful response
      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'text/html' },
        data: '<html><body>Hello World</body></html>',
      });

      const result = await fetchUrl('https://example.com');

      expect(result.success).toBe(true);
      expect(result.output).toContain('Hello World');
      expect(mockedAxios).toHaveBeenCalledWith(expect.objectContaining({
        method: 'GET',
        url: 'https://example.com',
      }));
    });

    it('should handle errors', async () => {
      // Mock error response
      mockedAxios.mockRejectedValueOnce(new Error('Network error'));

      const result = await fetchUrl('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });

    it('should handle different response types', async () => {
      // Mock JSON response
      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'application/json' },
        data: { message: 'Success' },
      });

      const result = await fetchUrl('https://example.com', 'GET', undefined, { responseType: 'json' });

      expect(result.success).toBe(true);
      expect(result.output).toContain('Success');
    });

    it('should use cached results when available', async () => {
      // Set up cache hit
      (fs.existsSync as jest.Mock).mockReturnValue(true);

      const result = await fetchUrl('https://example.com', 'GET', undefined, {
        cacheResults: true
      });

      expect(result.success).toBe(true);
      expect(result.output).toBe('Cached content');
      expect(result.metadata?.cached).toBe(true);
      expect(mockedAxios).not.toHaveBeenCalled();
    });

    it('should cache results when caching is enabled', async () => {
      // Set up cache miss but allow caching
      (fs.existsSync as jest.Mock).mockReturnValueOnce(false).mockReturnValueOnce(true);

      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'text/html' },
        data: 'Fresh content',
      });

      const result = await fetchUrl('https://example.com', 'GET', undefined, {
        cacheResults: true
      });

      expect(result.success).toBe(true);
      expect(result.output).toBe('Fresh content');
      expect(fs.writeFileSync).toHaveBeenCalled();
    });

    it('should respect rate limits', async () => {
      // Force rate limit to be exceeded
      // This requires modifying internal state of the module, which might not be possible in all test environments
      // For this test, we'll make multiple requests in quick succession

      for (let i = 0; i < 100; i++) {
        await fetchUrl('https://example.com', 'GET', undefined, {
          cacheResults: false
        });
      }

      // Reset mocks to check the next call
      vi.clearAllMocks();

      const result = await fetchUrl('https://example.com', 'GET', undefined, {
        cacheResults: false
      });

      // The result should indicate rate limiting
      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
      expect(mockedAxios).not.toHaveBeenCalled();
    });
  });

  describe('scrapeWebpage', () => {
    it('should scrape content from a webpage', async () => {
      // Mock fetchUrl to return HTML content
      vi.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve('<html><body><h1>Title</h1><p>Paragraph</p></body></html>'),
      } as Response);

      const result = await scrapeWebpage('https://example.com', { selector: 'h1', extractContents: true });

      expect(result.success).toBe(true);
      // The actual test would depend on the implementation of scrapeWebpage
    });
  });

  describe('makeApiRequest', () => {
    it('should make an API request', async () => {
      // Mock successful API response
      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'application/json' },
        data: { result: 'API response' },
      });

      const result = await makeApiRequest(
        'https://api.example.com',
        'POST',
        JSON.stringify({ query: 'test' }),
        {
          apiKey: 'test-key',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      expect(result.success).toBe(true);
      expect(result.output).toContain('API response');
      expect(mockedAxios).toHaveBeenCalledWith(expect.objectContaining({
        method: 'POST',
        url: 'https://api.example.com',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-key',
        }),
      }));
    });
  });

  describe('performSearch', () => {
    it('should perform a search with Google provider', async () => {
      // Mock fetchUrl to return search results
      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'application/json' },
        data: {
          items: [
            {
              title: 'Result 1',
              link: 'https://example.com/1',
              snippet: 'Description 1',
            },
            {
              title: 'Result 2',
              link: 'https://example.com/2',
              snippet: 'Description 2',
            },
          ],
        },
      });

      // Set environment variable for testing
      process.env.GOOGLE_SEARCH_CX = 'test-cx';

      const result = await performSearch({
        provider: 'google',
        query: 'test query',
        apiKey: 'test-key',
        numResults: 2,
      });

      expect(result.success).toBe(true);
      expect(result.output).toContain('Result 1');
      expect(result.output).toContain('Result 2');
    });

    it('should perform a search with DuckDuckGo provider', async () => {
      // Mock scrapeWebpage to return search results
      vi.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(`
          <div class="result">
            <a class="result__a">Result 1</a>
            <a class="result__url">https://example.com/1</a>
            <a class="result__snippet">Description 1</a>
          </div>
          <div class="result">
            <a class="result__a">Result 2</a>
            <a class="result__url">https://example.com/2</a>
            <a class="result__snippet">Description 2</a>
          </div>
        `),
      } as Response);

      const result = await performSearch({
        provider: 'duckduckgo',
        query: 'test query',
        numResults: 2,
      });

      expect(result.success).toBe(true);
      // The actual test would depend on the implementation of performSearch
    });

    it('should perform a search with GitHub provider', async () => {
      // Mock fetchUrl to return GitHub search results
      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'application/json' },
        data: {
          items: [
            {
              full_name: 'user/repo1',
              html_url: 'https://github.com/user/repo1',
              description: 'Repository 1 description',
              stargazers_count: 100,
              forks_count: 50,
              language: 'JavaScript',
            },
            {
              full_name: 'user/repo2',
              html_url: 'https://github.com/user/repo2',
              description: 'Repository 2 description',
              stargazers_count: 200,
              forks_count: 75,
              language: 'TypeScript',
            },
          ],
        },
      });

      const result = await performSearch({
        provider: 'github',
        query: 'test repository',
        numResults: 2,
      });

      expect(result.success).toBe(true);
      expect(result.output).toContain('user/repo1');
      expect(result.output).toContain('user/repo2');
      expect(result.output).toContain('Stars: 100');
      expect(result.output).toContain('JavaScript');
    });

    it('should perform a search with StackOverflow provider', async () => {
      // Mock scrapeWebpage to return StackOverflow search results
      vi.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(`
          <div class="question-summary">
            <a class="question-hyperlink" href="/questions/1">Question 1</a>
            <div class="excerpt">Question 1 description</div>
            <div class="vote-count-post">10</div>
            <div class="status"><strong>2</strong></div>
          </div>
          <div class="question-summary">
            <a class="question-hyperlink" href="/questions/2">Question 2</a>
            <div class="excerpt">Question 2 description</div>
            <div class="vote-count-post">5</div>
            <div class="status"><strong>1</strong></div>
          </div>
        `),
      } as Response);

      const result = await performSearch({
        provider: 'stackoverflow',
        query: 'test question',
        numResults: 2,
      });

      expect(result.success).toBe(true);
      expect(result.output).toContain('Question 1');
      expect(result.output).toContain('Question 2');
    });

    it('should apply search filters', async () => {
      // Mock fetchUrl to return search results
      mockedAxios.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'application/json' },
        data: {
          items: [
            {
              title: 'Result 1',
              link: 'https://example.com/1',
              snippet: 'Description 1',
            },
          ],
        },
      });

      // Set environment variable for testing
      process.env.GOOGLE_SEARCH_CX = 'test-cx';

      const result = await performSearch({
        provider: 'google',
        query: 'test query',
        apiKey: 'test-key',
        numResults: 1,
        filters: {
          siteRestriction: 'example.com',
          fileType: 'pdf',
          timeRange: 'week',
          language: 'en',
        },
      });

      expect(result.success).toBe(true);
      // The query should include the site restriction and file type
      expect(mockedAxios).toHaveBeenCalledWith(expect.objectContaining({
        url: expect.stringMatching(/test%20query%20site%3Aexample\.com%20filetype%3Apdf/),
      }));
    });
  });
});
