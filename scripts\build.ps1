# PowerShell script to build both alive-rs and alive-cli components
Write-Host "Building Alive AI components..." -ForegroundColor Cyan

# Build the Rust component (alive-rs)
Write-Host "Building alive-rs (Rust component)..." -ForegroundColor Green
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path "$scriptPath\..\alive-rs"
cargo build --release
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to build alive-rs" -ForegroundColor Red
    exit $LASTEXITCODE
}

# Build the CLI component (alive-cli)
Write-Host "Building alive-cli (Node.js component)..." -ForegroundColor Green
Set-Location -Path "$scriptPath\..\alive-cli"
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install Node.js dependencies" -ForegroundColor Red
    exit $LASTEXITCODE
}

npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to build alive-cli" -ForegroundColor Red
    exit $LASTEXITCODE
}

Write-Host "Build completed successfully!" -ForegroundColor Cyan 