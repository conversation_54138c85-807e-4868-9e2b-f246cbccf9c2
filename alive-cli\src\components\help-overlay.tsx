import { Box, Text, useInput } from "ink";
import React from "react";

/**
 * An overlay that lists the available slash‑commands and their description.
 * The overlay is purely informational and can be dismissed with the Escape
 * key. Keeping the implementation extremely small avoids adding any new
 * dependencies or complex state handling.
 */
export default function HelpOverlay({
  onExit,
}: {
  onExit: () => void;
}): JSX.Element {
  useInput((input, key) => {
    if (key.escape || input === "q") {
      onExit();
    }
  });

  return (
    <Box
      flexDirection="column"
      borderStyle="round"
      borderColor="blueBright"
      width={80}
    >
      <Box paddingX={2} paddingY={1} borderStyle="round" borderColor="gray">
        <Text bold color="blueBright">Alive AI Help</Text>
      </Box>

      <Box flexDirection="row" paddingX={2} paddingY={1}>
        <Box width="50%" flexDirection="column">
          <Box marginBottom={1}>
            <Text bold color="greenBright">
              Slash Commands
            </Text>
          </Box>

          <Box flexDirection="column" gap={1}>
            <CommandItem
              command="/help"
              description="Show this help overlay"
            />
            <CommandItem
              command="/model"
              description="Switch the LLM model in‑session"
            />
            <CommandItem
              command="/approval"
              description="Switch auto‑approval mode"
            />
            <CommandItem
              command="/history"
              description="Show command & file history for this session"
            />
            <CommandItem
              command="/sessions"
              description="Browse previous sessions"
            />
            <CommandItem
              command="/clear"
              description="Clear screen & context"
            />
            <CommandItem
              command="/clearhistory"
              description="Clear command history"
            />
            <CommandItem
              command="/bug"
              description="Generate a GitHub issue URL with session log"
            />
            <CommandItem
              command="/diff"
              description="View working tree git diff"
            />
            <CommandItem
              command="/compact"
              description="Condense context into a summary"
            />
            <CommandItem
              command="/health"
              description="Check system health and connectivity"
            />
            <CommandItem
              command="/theme"
              description="Change the UI theme and appearance"
            />
          </Box>
        </Box>

        <Box width="50%" flexDirection="column">
          <Box marginBottom={1}>
            <Text bold color="yellowBright">
              Keyboard Shortcuts
            </Text>
          </Box>

          <Box flexDirection="column" gap={1}>
            <ShortcutItem
              keys="Enter"
              description="Send message"
            />
            <ShortcutItem
              keys="Shift+Enter"
              description="Insert newline"
            />
            <ShortcutItem
              keys="Ctrl+J"
              description="Insert newline (alternative)"
            />
            <ShortcutItem
              keys="Tab"
              description="Cycle through suggestions"
            />
            <ShortcutItem
              keys="Shift+Tab"
              description="Cycle suggestions backwards"
            />
            <ShortcutItem
              keys="Up/Down"
              description="Navigate history or suggestions"
            />
            <ShortcutItem
              keys="Esc (twice)"
              description="Interrupt current action"
            />
            <ShortcutItem
              keys="Ctrl+C"
              description="Quit Alive"
            />
            <ShortcutItem
              keys="/"
              description="Start typing a command"
            />
            <ShortcutItem
              keys="@path"
              description="File path completion"
            />
            <ShortcutItem
              keys="q"
              description="Close overlay screens"
            />
          </Box>
        </Box>
      </Box>

      <Box paddingX={2} paddingY={1} borderStyle="round" borderColor="gray">
        <Box flexDirection="row" justifyContent="space-between">
          <Text dimColor>Press <Text bold>Esc</Text> or <Text bold>q</Text> to close</Text>
          <Text dimColor>Alive AI v1.0</Text>
        </Box>
      </Box>
    </Box>
  );
}

// Helper component for command items
function CommandItem({ command, description }: { command: string; description: string }) {
  return (
    <Box>
      <Text>
        <Text color="greenBright" bold>{command}</Text>
        <Text dimColor> – </Text>
        <Text>{description}</Text>
      </Text>
    </Box>
  );
}

// Helper component for shortcut items
function ShortcutItem({ keys, description }: { keys: string; description: string }) {
  return (
    <Box>
      <Text>
        <Text color="yellowBright" bold>{keys}</Text>
        <Text dimColor> – </Text>
        <Text>{description}</Text>
      </Text>
    </Box>
  );
}
