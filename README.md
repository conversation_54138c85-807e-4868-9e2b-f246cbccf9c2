# Alive AI

Alive AI is a powerful AI assistant that runs locally on your machine. It combines a Rust-based backend with a Node.js CLI interface to provide a seamless AI experience.

## Features

- **Terminal UI**: Rich interactive terminal user interface
- **File Operations**: Safely read, write, and manipulate files
- **Shell Integration**: Run shell commands directly from the AI
- **Web Access**: Fetch data from the web, perform searches, and interact with APIs (when enabled)
- **Sandbox Security**: Configurable security policies to keep your system safe
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Rust (latest stable)
- Node.js (v18 or higher)

### Automated Installation

#### Windows

Run the PowerShell installation script (requires administrator privileges):

```powershell
# Run from the root of the project
.\scripts\install.ps1
```

#### macOS/Linux

Run the shell installation script:

```bash
# Run from the root of the project
./scripts/install.sh
```

### Building

#### Windows

```powershell
.\scripts\build.ps1
```

#### macOS/Linux

```bash
./scripts/build.sh
```

## Usage

### Starting Alive AI

#### Windows

```powershell
.\scripts\run.ps1
```

#### macOS/Linux

```bash
./scripts/run.sh
```

### Command-Line Options

```
Usage: alive-tui [OPTIONS]

Options:
  -m, --model <MODEL>                     AI model to use [default: is specified in config.toml]
  -p, --profile <PROFILE>                 Configuration profile to use
      --full-auto                         Enable auto-approval of commands
  -a, --approval-policy <APPROVAL_POLICY> Execution approval policy [possible values: unless-allow-listed, auto-edit, on-failure, never]
      --sandbox <PERMISSIONS>...          Sandbox permissions
  -c, --cwd <CWD>                         Working directory for this session
      --skip-git-repo-check               Skip the check that verifies you're in a Git repo
      --disable-response-storage          Disable response storage
      --prompt <PROMPT>                   Initial prompt
  -i, --image <IMAGE>...                  Include image file(s) with the initial prompt
      --enable-web-tool                   Enable the web tool for fetching data from the internet (requires full-auto mode)
      --google-api-key <KEY>              Set the Google API key for web search
      --bing-api-key <KEY>                Set the Bing API key for web search
      --google-cx <ID>                    Set the Google Custom Search Engine ID for search
      --search-provider <PROVIDER>        Set the default search provider (google, bing, duckduckgo)
  -h, --help                              Print help
  -V, --version                           Print version
```

## Web Tool

Alive AI includes a web tool that allows agents to fetch data from the internet, interact with APIs, and perform web searches.

### Setup

To set up the web tool dependencies, run:

```bash
# From the alive-cli directory
npm run setup-web-tool
```

### Usage

To enable the web tool, use the `--enable-web-tool` flag:

```bash
alive --enable-web-tool --full-auto "Fetch the latest news about AI"
```

### Configuration

You can manage your web tool API keys in several ways:

1. **Web Key Management Utility**: 
   ```bash
   npm run manage-web-keys
   ```
   This interactive utility allows you to view, update, and clear API keys for web search providers.

2. **Command Line Arguments**:
   ```bash
   # Google Search
   alive --enable-web-tool --google-api-key "YOUR_KEY" --google-cx "YOUR_CX" --search-provider "google" "Search for..."
   
   # Bing Search
   alive --enable-web-tool --bing-api-key "YOUR_KEY" --search-provider "bing" "Search for..."
   
   # DuckDuckGo (no API key required)
   alive --enable-web-tool --search-provider "duckduckgo" "Search for..."
   ```

3. **Environment Variables**:
   - `GOOGLE_API_KEY`: Google API key
   - `GOOGLE_SEARCH_CX`: Google Custom Search Engine ID
   - `BING_API_KEY`: Bing API key
   - `ALIVE_DEFAULT_SEARCH_PROVIDER`: Default search provider

For more details, see the [web tool documentation](docs/web-tool.md).

## Architecture

Alive AI consists of two main components:

1. **alive-rs** - A Rust-based backend that:
   - Manages model communication
   - Handles file system operations
   - Executes shell commands
   - Enforces security policies
   - Provides the terminal UI

2. **alive-cli** - A Node.js CLI interface that:
   - Provides a user-friendly command-line experience
   - Manages configuration
   - Integrates with the Rust backend

## Security

Alive AI takes security seriously. By default, it operates with restricted permissions:

- Read-only access to most of the file system
- Write access only to the current directory
- No network access by default (can be enabled with flags)

You can configure the security policy to match your needs with the `--sandbox` flag.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
