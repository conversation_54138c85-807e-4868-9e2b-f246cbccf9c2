# PowerShell script to run Alive AI
Write-Host "Starting Alive AI..." -ForegroundColor Cyan

# Get the script path
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Check if build is needed
if (-not (Test-Path "$scriptPath\..\alive-rs\target\release\alive-tui.exe")) {
    Write-Host "Build required. Running build script first..." -ForegroundColor Yellow
    & "$scriptPath\build.ps1"
    if ($LASTEXITCODE -ne 0) {
        exit $LASTEXITCODE
    }
}

# Run the TUI interface
Write-Host "Launching Alive AI TUI..." -ForegroundColor Green
Set-Location -Path "$scriptPath\..\alive-rs"
& ".\target\release\alive-tui.exe" $args 