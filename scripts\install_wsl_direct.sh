#!/bin/bash
# Direct installation script for Alive AI in WSL
# This script clones the repository, builds the project, and installs it globally

set -euo pipefail

echo "===== Alive AI WSL Direct Installation ====="
echo "This script will install Alive AI directly from GitHub."

# Check for required tools
for cmd in git node npm; do
  if ! command -v $cmd &> /dev/null; then
    echo "Error: $cmd is required but not installed."
    if [ "$cmd" = "node" ] || [ "$cmd" = "npm" ]; then
      echo "Please install Node.js 22 or newer:"
      echo "  curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -"
      echo "  sudo apt-get install -y nodejs"
    elif [ "$cmd" = "git" ]; then
      echo "Please install git:"
      echo "  sudo apt-get install -y git"
    fi
    exit 1
  fi
done

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 22 ]; then
  echo "Node.js 22 or newer is required. Found version: $(node -v)"
  echo "Please upgrade Node.js:"
  echo "  curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -"
  echo "  sudo apt-get install -y nodejs"
  exit 1
fi

# Create a temporary directory
TMP_DIR=$(mktemp -d)
echo "Working in temporary directory: $TMP_DIR"

# Clone the repository
echo "Cloning Alive AI repository..."
git clone https://github.com/Aliveaicom/Alive-AI.git "$TMP_DIR/alive-ai"
cd "$TMP_DIR/alive-ai"

# Install pnpm locally instead of globally to avoid permission issues
echo "Installing pnpm locally..."
npm install pnpm@10.8.1

# Use the locally installed pnpm
PNPM="$TMP_DIR/alive-ai/node_modules/.bin/pnpm"

# Install dependencies
echo "Installing dependencies..."
$PNPM install

# Build the project
echo "Building Alive AI..."
cd alive-cli
$PNPM run build

# Create installation directory
INSTALL_DIR="$HOME/.alive-ai"
mkdir -p "$INSTALL_DIR/bin"
mkdir -p "$INSTALL_DIR/dist"

# Copy necessary files
echo "Installing Alive AI to $INSTALL_DIR..."
cp bin/alive.js "$INSTALL_DIR/bin/"
cp -r dist/* "$INSTALL_DIR/dist/"

# Create package.json in installation directory
cat > "$INSTALL_DIR/package.json" << EOF
{
  "name": "alive-ai",
  "version": "0.1.0",
  "description": "Alive AI CLI",
  "type": "module",
  "bin": {
    "alive": "bin/alive.js"
  },
  "engines": {
    "node": ">=22.0.0"
  },
  "dependencies": {
    "@alive-ai/cli": "0.0.0-dev"
  }
}
EOF

# Create executable script in user's bin directory
USER_BIN_DIR="$HOME/.local/bin"
mkdir -p "$USER_BIN_DIR"
EXEC_PATH="$USER_BIN_DIR/alive"

echo "Creating executable at $EXEC_PATH..."
cat > "$EXEC_PATH" << EOF
#!/usr/bin/env node
require('$HOME/.alive-ai/bin/alive.js');
EOF

chmod +x "$EXEC_PATH"

# Add to PATH if not already there
if [[ ":$PATH:" != *":$USER_BIN_DIR:"* ]]; then
  echo ""
  echo "Adding $USER_BIN_DIR to your PATH..."
  echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
  echo "Please run 'source ~/.bashrc' after installation to update your PATH"
fi

# Clean up
echo "Cleaning up..."
rm -rf "$TMP_DIR"

# Configure API key if not already set
if [ -z "${OPENAI_API_KEY:-}" ]; then
  echo ""
  echo "IMPORTANT: You need to set your OpenAI API key to use Alive AI."
  echo "Run the following command with your API key:"
  echo "  export OPENAI_API_KEY=\"your-api-key\""
  echo ""
  echo "To make it persistent, add it to your ~/.bashrc or ~/.zshrc file:"
  echo "  echo 'export OPENAI_API_KEY=\"your-api-key\"' >> ~/.bashrc"
  echo "  source ~/.bashrc"
fi

echo ""
echo "===== Installation Complete ====="
echo "Alive AI has been successfully installed!"
echo "You can now run 'alive' from anywhere in your WSL environment."
echo ""
echo "Example usage:"
echo "  alive \"Write and run a python program that prints ASCII art\""
echo ""
echo "For more information, visit: https://github.com/Aliveaicom/Alive-AI"
