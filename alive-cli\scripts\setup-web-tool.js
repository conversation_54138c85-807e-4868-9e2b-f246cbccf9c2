#!/usr/bin/env node
import { exec } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import { existsSync } from 'fs';

// Get the directory where this script is located
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = resolve(__dirname, '..');

console.log("Setting up Alive AI Web Tool dependencies...");

/**
 * Execute a shell command and return a Promise
 */
function execPromise(command, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`Executing: ${command}`);
    exec(command, options, (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }
      
      console.log(stdout);
      if (stderr) console.error(stderr);
      resolve({ stdout, stderr });
    });
  });
}

/**
 * Determine which package manager to use
 */
async function detectPackageManager() {
  try {
    // Check if we're inside a pnpm project
    if (existsSync(resolve(rootDir, 'pnpm-lock.yaml'))) {
      return 'pnpm';
    }
    
    // Check if yarn is installed and we're in a yarn project
    if (existsSync(resolve(rootDir, 'yarn.lock'))) {
      try {
        await execPromise('yarn --version', { stdio: 'ignore' });
        return 'yarn';
      } catch (e) {
        // Yarn lock exists but yarn command not found
        console.log('Yarn lock detected but yarn command not found, falling back to npm');
      }
    }
    
    // Default to npm
    return 'npm';
  } catch (e) {
    // Default to npm if all else fails
    return 'npm';
  }
}

/**
 * Main setup function
 */
async function setup() {
  try {
    // Detect package manager
    const pkgManager = await detectPackageManager();
    console.log(`Using package manager: ${pkgManager}`);
    
    // Install runtime dependencies
    console.log("Installing required runtime dependencies...");
    const dependencies = ['axios', 'cheerio', 'node-abort-controller'];
    
    const installCmd = 
      pkgManager === 'yarn' 
        ? `yarn add ${dependencies.join(' ')}`
        : pkgManager === 'pnpm'
          ? `pnpm add ${dependencies.join(' ')}`
          : `npm install ${dependencies.join(' ')}`;
    
    await execPromise(installCmd, { cwd: rootDir });
    
    // Install dev dependencies (types)
    console.log("\nInstalling TypeScript type definitions...");
    const devDependencies = ['@types/cheerio'];
    
    const installDevCmd = 
      pkgManager === 'yarn' 
        ? `yarn add --dev ${devDependencies.join(' ')}`
        : pkgManager === 'pnpm'
          ? `pnpm add --save-dev ${devDependencies.join(' ')}`
          : `npm install --save-dev ${devDependencies.join(' ')}`;
    
    await execPromise(installDevCmd, { cwd: rootDir });
    
    // Verify installation by importing the modules
    console.log("\nVerifying installation...");
    const verifyScript = `
      import axios from 'axios';
      import * as cheerio from 'cheerio';
      import { AbortController } from 'node-abort-controller';
      console.log('All dependencies loaded successfully!');
    `;
    
    const tempFile = resolve(rootDir, 'temp-verify.mjs');
    const fs = await import('fs/promises');
    await fs.writeFile(tempFile, verifyScript);
    
    try {
      // Use double quotes around the file path to handle spaces
      await execPromise(`node "${tempFile}"`, { cwd: rootDir });
      console.log("✅ Verification successful!");
    } catch (e) {
      console.error("⚠️ Verification failed. Some dependencies may not be properly installed.");
      console.error(e);
    } finally {
      await fs.unlink(tempFile).catch(() => {});
    }
    
    // Success message
    console.log("\n✅ Web tool dependencies successfully installed!");
    console.log("\nYou can now use the web tool with:");
    console.log('  alive --enable-web-tool --full-auto "your prompt here"');
    console.log("\nTo configure API keys, you can set them as environment variables:");
    console.log("  export GOOGLE_API_KEY=your_api_key");
    console.log("  export GOOGLE_SEARCH_CX=your_search_engine_id");
    console.log("  export BING_API_KEY=your_api_key");
    console.log("  export ALIVE_DEFAULT_SEARCH_PROVIDER=google|bing|duckduckgo");
    console.log("\nOr use them as command-line arguments:");
    console.log('  alive --enable-web-tool --google-api-key "your_api_key" --google-cx "your_cx" --search-provider "google" "your prompt"');
    console.log("\nYou can also run the demo with:");
    console.log("  node alive-cli/examples/web-tool-demo.js");
    
  } catch (error) {
    console.error("\n❌ Error setting up web tool dependencies:");
    console.error(error);
    process.exit(1);
  }
}

// Run the setup
setup(); 