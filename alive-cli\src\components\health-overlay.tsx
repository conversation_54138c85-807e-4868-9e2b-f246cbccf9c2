import { Box, Text, useInput } from "ink";
import React, { useEffect, useState } from "react";
import os from "os";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

interface HealthStatus {
  name: string;
  status: "checking" | "ok" | "warning" | "error";
  message: string;
}

/**
 * Overlay component that displays system health and connectivity information
 */
export default function HealthOverlay({
  onExit,
}: {
  onExit: () => void;
}): JSX.Element {
  const [healthChecks, setHealthChecks] = useState<HealthStatus[]>([
    { name: "System Info", status: "checking", message: "Checking..." },
    { name: "Network Connectivity", status: "checking", message: "Checking..." },
    { name: "API Connectivity", status: "checking", message: "Checking..." },
    { name: "Git Status", status: "checking", message: "Checking..." },
    { name: "Disk Space", status: "checking", message: "Checking..." },
  ]);

  useEffect(() => {
    const runHealthChecks = async () => {
      // System info check
      try {
        const platform = os.platform();
        const release = os.release();
        const totalMem = Math.round(os.totalmem() / (1024 * 1024 * 1024));
        const freeMem = Math.round(os.freemem() / (1024 * 1024 * 1024));
        const usedMemPercent = Math.round(
          ((totalMem - freeMem) / totalMem) * 100
        );

        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "System Info"
              ? {
                  name: "System Info",
                  status: "ok",
                  message: `${platform} ${release}, Memory: ${freeMem}GB free of ${totalMem}GB (${usedMemPercent}% used)`,
                }
              : check
          )
        );
      } catch (error) {
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "System Info"
              ? {
                  name: "System Info",
                  status: "error",
                  message: `Error getting system info: ${error}`,
                }
              : check
          )
        );
      }

      // Network connectivity check
      try {
        const { stdout: pingResult } = await execAsync(
          "ping -c 1 -W 2 openai.com || ping -n 1 -w 2000 openai.com"
        );
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "Network Connectivity"
              ? {
                  name: "Network Connectivity",
                  status: "ok",
                  message: "Internet connection is active",
                }
              : check
          )
        );
      } catch (error) {
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "Network Connectivity"
              ? {
                  name: "Network Connectivity",
                  status: "error",
                  message: "Cannot reach internet. Check your connection",
                }
              : check
          )
        );
      }

      // API connectivity check
      try {
        const { stdout: curlResult } = await execAsync(
          "curl -s -o /dev/null -w '%{http_code}' https://api.openai.com/v1/models"
        );
        const statusCode = parseInt(curlResult.trim(), 10);
        
        if (statusCode >= 200 && statusCode < 500) {
          setHealthChecks((prev) =>
            prev.map((check) =>
              check.name === "API Connectivity"
                ? {
                    name: "API Connectivity",
                    status: "ok",
                    message: "OpenAI API is reachable",
                  }
                : check
            )
          );
        } else {
          setHealthChecks((prev) =>
            prev.map((check) =>
              check.name === "API Connectivity"
                ? {
                    name: "API Connectivity",
                    status: "warning",
                    message: `OpenAI API returned status ${statusCode}`,
                  }
                : check
            )
          );
        }
      } catch (error) {
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "API Connectivity"
              ? {
                  name: "API Connectivity",
                  status: "error",
                  message: "Cannot connect to OpenAI API",
                }
              : check
          )
        );
      }

      // Git status check
      try {
        const { stdout: gitResult } = await execAsync("git status");
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "Git Status"
              ? {
                  name: "Git Status",
                  status: "ok",
                  message: "Git repository is accessible",
                }
              : check
          )
        );
      } catch (error) {
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "Git Status"
              ? {
                  name: "Git Status",
                  status: "warning",
                  message: "Not in a git repository or git not installed",
                }
              : check
          )
        );
      }

      // Disk space check
      try {
        const { stdout: dfResult } = await execAsync(
          process.platform === "win32"
            ? "wmic logicaldisk get freespace,size,caption"
            : "df -h /"
        );
        
        if (process.platform === "win32") {
          // Simple parsing for Windows, could be improved
          setHealthChecks((prev) =>
            prev.map((check) =>
              check.name === "Disk Space"
                ? {
                    name: "Disk Space",
                    status: "ok",
                    message: "Disk space information available",
                  }
                : check
            )
          );
        } else {
          // Linux/macOS parsing
          const lines = dfResult.split("\n");
          if (lines.length >= 2) {
            const parts = lines[1].split(/\s+/);
            if (parts.length >= 5) {
              const usedPercent = parts[4].replace("%", "");
              const usedPercentNum = parseInt(usedPercent, 10);
              
              setHealthChecks((prev) =>
                prev.map((check) =>
                  check.name === "Disk Space"
                    ? {
                        name: "Disk Space",
                        status: 
                          usedPercentNum > 90
                            ? "error"
                            : usedPercentNum > 75
                            ? "warning"
                            : "ok",
                        message: `Disk usage: ${parts[4]} (${parts[2]} used, ${parts[3]} available)`,
                      }
                    : check
                )
              );
            }
          }
        }
      } catch (error) {
        setHealthChecks((prev) =>
          prev.map((check) =>
            check.name === "Disk Space"
              ? {
                  name: "Disk Space",
                  status: "error",
                  message: "Error checking disk space",
                }
              : check
          )
        );
      }
    };

    runHealthChecks();
  }, []);

  // Handle keyboard input for exiting
  useInput((input, key) => {
    if (key.escape || input === "q") {
      onExit();
    }
  });

  const getStatusColor = (status: HealthStatus["status"]) => {
    switch (status) {
      case "ok":
        return "green";
      case "warning":
        return "yellow";
      case "error":
        return "red";
      default:
        return "gray";
    }
  };

  const getStatusSymbol = (status: HealthStatus["status"]) => {
    switch (status) {
      case "ok":
        return "✓";
      case "warning":
        return "⚠";
      case "error":
        return "✗";
      default:
        return "○";
    }
  };

  return (
    <Box
      flexDirection="column"
      borderStyle="round"
      borderColor="blueBright"
      width={80}
    >
      <Box paddingX={2} paddingY={1} borderStyle="round" borderColor="gray">
        <Text bold color="blueBright">System Health Check</Text>
      </Box>

      <Box flexDirection="column" paddingX={2} paddingY={1}>
        <Text bold>Alive AI System Diagnostics:</Text>
        <Box marginY={1} />
        
        {healthChecks.map((check) => (
          <Box key={check.name} marginY={1} flexDirection="column">
            <Text>
              <Text color={getStatusColor(check.status)} bold>
                {getStatusSymbol(check.status)} {check.name}
              </Text>
            </Text>
            <Box marginLeft={2}>
              <Text dimColor={check.status === "ok"}>
                {check.message}
              </Text>
            </Box>
          </Box>
        ))}

        <Box marginY={1} />
        <Text dimColor>
          Press <Text bold>Esc</Text> or <Text bold>q</Text> to close
        </Text>
      </Box>
    </Box>
  );
} 