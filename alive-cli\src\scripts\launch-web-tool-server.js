#!/usr/bin/env node
/**
 * Web Tool Server Launcher Script
 *
 * This is a standalone script to launch the web tool server without React dependencies.
 * It avoids the React hook errors by running in a separate process.
 */

// Import required modules
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Suppress React-related errors and warnings
const originalConsoleError = console.error;
console.error = function(...args) {
  const errorMessage = args[0]?.toString() || '';
  if (errorMessage.includes('React') ||
      errorMessage.includes('hook') ||
      errorMessage.includes('useState') ||
      errorMessage.includes('Cannot read properties of null')) {
    // Suppress React-related errors
    return;
  }

  // Pass through all other errors
  return originalConsoleError.apply(console, args);
};

// Get the web tool keys from environment variables or auth.json
function getWebToolKeys() {
  // Start with environment variables
  const keys = {
    googleApiKey: process.env.GOOGLE_API_KEY || '',
    bingApiKey: process.env.BING_API_KEY || '',
    googleSearchCx: process.env.GOOGLE_SEARCH_CX || '',
    defaultSearchProvider: process.env.ALIVE_DEFAULT_SEARCH_PROVIDER || 'duckduckgo'
  };

  // Try to load from auth.json if environment variables are not set
  try {
    const home = os.homedir();
    const authDir = path.join(home, '.alive-ai');
    const authFile = path.join(authDir, 'auth.json');

    if (fs.existsSync(authFile)) {
      const data = JSON.parse(fs.readFileSync(authFile, 'utf-8'));

      if (data.webTool) {
        // Only override if environment variables are not set
        if (!keys.googleApiKey && data.webTool.googleApiKey) {
          keys.googleApiKey = data.webTool.googleApiKey;
        }

        if (!keys.bingApiKey && data.webTool.bingApiKey) {
          keys.bingApiKey = data.webTool.bingApiKey;
        }

        if (!keys.googleSearchCx && data.webTool.googleSearchCx) {
          keys.googleSearchCx = data.webTool.googleSearchCx;
        }

        if (!process.env.ALIVE_DEFAULT_SEARCH_PROVIDER && data.webTool.defaultSearchProvider) {
          keys.defaultSearchProvider = data.webTool.defaultSearchProvider;
        }
      }
    }
  } catch (error) {
    console.error('Error loading web tool keys from auth file:', error);
  }

  return keys;
}

const webToolKeys = getWebToolKeys();

// Function to start the web tool server
function startWebToolServer() {
  try {
    // Try multiple possible paths for the web tool server module
    const possiblePaths = [
      // Direct path in the same directory structure
      path.join(__dirname, '..', 'mcp-servers', 'web-tool-server.js'),
      // Path for compiled version
      path.join(__dirname, '..', 'mcp-servers', 'web-tool-server.js'),
      // Path for TypeScript version
      path.join(__dirname, '..', 'mcp-servers', 'web-tool-server.ts'),
      // Path for dist folder
      path.join(__dirname, '..', '..', 'dist', 'mcp-servers', 'web-tool-server.js'),
      // Path relative to current directory
      path.join(process.cwd(), 'src', 'mcp-servers', 'web-tool-server.js'),
      // Path for node_modules
      path.join(process.cwd(), 'node_modules', 'alive-cli', 'dist', 'mcp-servers', 'web-tool-server.js')
    ];

    let webToolServerPath = null;
    for (const p of possiblePaths) {
      if (fs.existsSync(p)) {
        webToolServerPath = p;
        break;
      }
    }

    if (!webToolServerPath) {
      console.error('Web Tool Server module not found in any of the expected locations');

      // Try to find it by searching in common directories
      const searchDirs = [
        path.join(process.cwd()),
        path.join(process.cwd(), 'src'),
        path.join(process.cwd(), 'dist'),
        path.join(__dirname, '..'),
        path.join(__dirname, '..', '..')
      ];

      for (const dir of searchDirs) {
        try {
          const files = fs.readdirSync(dir);
          console.log(`Searching in ${dir}: ${files.join(', ')}`);
        } catch (err) {
          // Ignore errors
        }
      }

      process.exit(1);
    }

    console.log(`Found Web Tool Server module at ${webToolServerPath}`);

    // Create a simple script to run the server
    const tempScriptContent = `
      try {
        const { startWebToolServer } = require('${webToolServerPath.replace(/\\/g, '\\\\')}');

        // Web tool keys from our getWebToolKeys function
        const webToolKeys = ${JSON.stringify(webToolKeys, null, 2)};

        // Start the server
        startWebToolServer(webToolKeys);

        // Log startup
        console.log('Web Tool MCP Server launched successfully');

        // Handle process signals
        process.on('SIGINT', () => {
          console.log('Web Tool MCP Server shutting down (SIGINT)');
          process.exit(0);
        });

        process.on('SIGTERM', () => {
          console.log('Web Tool MCP Server shutting down (SIGTERM)');
          process.exit(0);
        });
      } catch (error) {
        console.error('Error in Web Tool Server:', error);
        process.exit(1);
      }
    `;

    // Create a temporary file for the script
    const tempScriptPath = path.join(__dirname, 'temp-web-tool-server.js');
    fs.writeFileSync(tempScriptPath, tempScriptContent);

    // Start the server as a child process
    const webToolServer = spawn('node', [tempScriptPath], {
      detached: true,
      stdio: 'ignore',
      env: process.env
    });

    // Detach the child process
    webToolServer.unref();

    console.log('Web Tool MCP Server started successfully');

    // Clean up the temporary script after a delay
    setTimeout(() => {
      try {
        fs.unlinkSync(tempScriptPath);
      } catch (error) {
        // Ignore errors when cleaning up
      }
    }, 5000);

  } catch (error) {
    console.error('Error starting Web Tool MCP Server:', error);
    process.exit(1);
  }
}

// Start the server
startWebToolServer();
