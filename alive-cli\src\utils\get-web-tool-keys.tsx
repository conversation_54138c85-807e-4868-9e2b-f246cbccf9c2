import React from "react";
import { Box, Text, useInput } from "ink";
import TextInput from "ink-text-input";
import SelectInput from "ink-select-input";
import { updateWebToolConfig } from "./config.js";
import { homedir } from "os";
import { join } from "path";
import { existsSync, readFileSync, writeFileSync, mkdirSync } from "fs";

export type WebToolKeys = {
  googleApiKey?: string;
  bingApiKey?: string;
  googleSearchCx?: string;
  defaultSearchProvider?: 'google' | 'bing' | 'microsoft' | 'yahoo' | 'duckduckgo' | 'stackoverflow' | 'github';
};

/**
 * Load existing web tool keys from auth.json if available
 * This function is exported so it can be used by the MCP server
 */
export function loadExistingWebToolKeys(): WebToolKeys {
  try {
    const home = homedir();
    const authDir = join(home, ".alive-ai");
    const authFile = join(authDir, "auth.json");

    if (existsSync(authFile)) {
      const authData = JSON.parse(readFileSync(authFile, "utf8"));
      return {
        googleApiKey: authData.webTool?.googleApiKey || "",
        bingApiKey: authData.webTool?.bingApiKey || "",
        googleSearchCx: authData.webTool?.googleSearchCx || "",
        defaultSearchProvider: authData.webTool?.defaultSearchProvider || "duckduckgo"
      };
    }
  } catch (err) {
    // Ignore errors reading auth.json
    console.error('Error loading web tool keys:', err);
  }

  return {
    googleApiKey: "",
    bingApiKey: "",
    googleSearchCx: "",
    defaultSearchProvider: "duckduckgo"
  };
}

// Save web tool keys to auth.json
function saveWebToolKeys(keys: WebToolKeys): void {
  try {
    const home = homedir();
    const authDir = join(home, ".alive-ai");
    const authFile = join(authDir, "auth.json");

    // Create directory if it doesn't exist
    if (!existsSync(authDir)) {
      mkdirSync(authDir, { recursive: true });
    }

    // Read existing auth data or create new object
    let authData = {};
    if (existsSync(authFile)) {
      try {
        authData = JSON.parse(readFileSync(authFile, "utf8"));
      } catch (err) {
        // If file exists but isn't valid JSON, start with empty object
        authData = {};
      }
    }

    // Update web tool keys
    authData = {
      ...authData,
      webTool: {
        ...(authData.webTool || {}),
        googleApiKey: keys.googleApiKey,
        bingApiKey: keys.bingApiKey,
        googleSearchCx: keys.googleSearchCx,
        defaultSearchProvider: keys.defaultSearchProvider
      }
    };

    // Write updated auth data back to file
    writeFileSync(authFile, JSON.stringify(authData, null, 2), "utf8");

    // Also update config
    updateWebToolConfig(keys);
  } catch (err) {
    console.error(`Error saving web tool keys: ${err}`);
  }
}

enum ConfigStep {
  INTRO,
  GOOGLE_API_KEY,
  GOOGLE_CX,
  BING_API_KEY,
  DEFAULT_PROVIDER,
  CONFIRM,
  DONE,
  SKIP
}

const WebToolKeysInput: React.FC<{
  onDone: (keys: WebToolKeys) => void;
}> = ({ onDone }) => {
  const existingKeys = loadExistingWebToolKeys();
  const [step, setStep] = React.useState<ConfigStep>(ConfigStep.INTRO);
  const [googleApiKey, setGoogleApiKey] = React.useState<string>(existingKeys.googleApiKey || "");
  const [googleSearchCx, setGoogleSearchCx] = React.useState<string>(existingKeys.googleSearchCx || "");
  const [bingApiKey, setBingApiKey] = React.useState<string>(existingKeys.bingApiKey || "");
  const [defaultProvider, setDefaultProvider] = React.useState<'google' | 'bing' | 'duckduckgo'>(
    existingKeys.defaultSearchProvider as any || "duckduckgo"
  );

  useInput((input, key) => {
    if (key.escape) {
      setStep(ConfigStep.SKIP);
    }
  });

  const handleSubmit = () => {
    const keys: WebToolKeys = {
      googleApiKey: googleApiKey || undefined,
      bingApiKey: bingApiKey || undefined,
      googleSearchCx: googleSearchCx || undefined,
      defaultSearchProvider: defaultProvider
    };

    saveWebToolKeys(keys);
    onDone(keys);
    setStep(ConfigStep.DONE);
  };

  const providerItems = [
    { label: "DuckDuckGo (no API key required)", value: "duckduckgo" },
    { label: "Google (requires API key and CX)", value: "google" },
    { label: "Bing (requires API key)", value: "bing" },
    { label: "Microsoft (uses Bing API key)", value: "microsoft" },
    { label: "Yahoo (no API key required)", value: "yahoo" },
    { label: "StackOverflow (no API key required)", value: "stackoverflow" },
    { label: "GitHub (no API key required)", value: "github" }
  ];

  if (step === ConfigStep.SKIP) {
    onDone(existingKeys);
    return <Text>Web tool configuration skipped.</Text>;
  }

  if (step === ConfigStep.DONE) {
    return <Text>Web tool configuration complete!</Text>;
  }

  return (
    <Box flexDirection="column">
      {step === ConfigStep.INTRO && (
        <>
          <Text bold>Web Tool Configuration</Text>
          <Text>
            Configure API keys for web search providers. Press ESC at any time to skip.
          </Text>
          <Text>Press Enter to continue or ESC to skip.</Text>
          <Box marginTop={1}>
            <TextInput
              value=""
              onChange={() => {}}
              onSubmit={() => setStep(ConfigStep.GOOGLE_API_KEY)}
            />
          </Box>
        </>
      )}

      {step === ConfigStep.GOOGLE_API_KEY && (
        <>
          <Text bold>Google API Key</Text>
          <Text>
            Enter your Google API key for search operations (leave empty to skip):
          </Text>
          <Box marginTop={1}>
            <TextInput
              value={googleApiKey}
              onChange={setGoogleApiKey}
              onSubmit={() => setStep(ConfigStep.GOOGLE_CX)}
              placeholder="Google API Key"
            />
          </Box>
        </>
      )}

      {step === ConfigStep.GOOGLE_CX && (
        <>
          <Text bold>Google Custom Search Engine ID</Text>
          <Text>
            Enter your Google Custom Search Engine ID (leave empty to skip):
          </Text>
          <Box marginTop={1}>
            <TextInput
              value={googleSearchCx}
              onChange={setGoogleSearchCx}
              onSubmit={() => setStep(ConfigStep.BING_API_KEY)}
              placeholder="Google Custom Search Engine ID"
            />
          </Box>
        </>
      )}

      {step === ConfigStep.BING_API_KEY && (
        <>
          <Text bold>Bing API Key</Text>
          <Text>
            Enter your Bing API key for search operations (leave empty to skip):
          </Text>
          <Box marginTop={1}>
            <TextInput
              value={bingApiKey}
              onChange={setBingApiKey}
              onSubmit={() => setStep(ConfigStep.DEFAULT_PROVIDER)}
              placeholder="Bing API Key"
            />
          </Box>
        </>
      )}

      {step === ConfigStep.DEFAULT_PROVIDER && (
        <>
          <Text bold>Default Search Provider</Text>
          <Text>Select your default search provider:</Text>
          <Box marginTop={1}>
            <SelectInput
              items={providerItems}
              initialIndex={providerItems.findIndex(item => item.value === defaultProvider)}
              onSelect={(item) => {
                setDefaultProvider(item.value as any);
                setStep(ConfigStep.CONFIRM);
              }}
            />
          </Box>
        </>
      )}

      {step === ConfigStep.CONFIRM && (
        <>
          <Text bold>Confirm Configuration</Text>
          <Text>Google API Key: {googleApiKey || "(not set)"}</Text>
          <Text>Google CX: {googleSearchCx || "(not set)"}</Text>
          <Text>Bing API Key: {bingApiKey || "(not set)"}</Text>
          <Text>Default Provider: {defaultProvider}</Text>
          <Text marginTop={1}>Press Enter to save or ESC to cancel.</Text>
          <Box marginTop={1}>
            <TextInput
              value=""
              onChange={() => {}}
              onSubmit={handleSubmit}
            />
          </Box>
        </>
      )}
    </Box>
  );
};

export async function getWebToolKeys(): Promise<WebToolKeys> {
  return new Promise((resolve) => {
    const render = require("ink").render;

    const { unmount } = render(
      <WebToolKeysInput
        onDone={(keys) => {
          unmount();
          resolve(keys);
        }}
      />
    );
  });
}
