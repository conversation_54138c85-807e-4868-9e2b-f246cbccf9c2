@echo off
setlocal enabledelayedexpansion
title Alive AI Launcher
color 0B

REM Set the base directory
set "BASE_DIR=%~dp0"
cd /d "%BASE_DIR%"

REM Create preferences directory if it doesn't exist
set "PREFS_DIR=%USERPROFILE%\.alive-ai"
if not exist "%PREFS_DIR%" mkdir "%PREFS_DIR%"
set "PREFS_FILE=%PREFS_DIR%\launcher_prefs.txt"

REM Check for API keys in Windows and load them
set PROVIDER_FLAG=

REM Try to read API keys from auth.json
set AUTH_FILE=%USERPROFILE%\.alive-ai\auth.json
if exist "%AUTH_FILE%" (
    echo Loading API keys from auth.json...
    for /f "tokens=*" %%a in ('powershell -Command "try { (Get-Content -Raw '%AUTH_FILE%' | ConvertFrom-Json).providerKeys.deepseek } catch { '' }"') do (
        set DEEPSEEK_API_KEY=%%a
    )
)

REM If we have a DeepSeek API key, use DeepSeek provider by default
if defined DEEPSEEK_API_KEY (
    echo Using DeepSeek provider...
    set PROVIDER_FLAG=--provider deepseek
) else (
    echo No DeepSeek API key found, will attempt to use default provider...
)

REM Check if WSL is installed
set WSL_AVAILABLE=0
wsl --status >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set WSL_AVAILABLE=1
)

REM Check if versions are available
set RUST_AVAILABLE=0
set NODEJS_AVAILABLE=0

if exist "alive-rs" (
    set RUST_AVAILABLE=1
) else (
    if exist "target" (
        set RUST_AVAILABLE=1
    )
)

if exist "alive-cli" (
    set NODEJS_AVAILABLE=1

    REM Check if the Node.js version is built
    if not exist "alive-cli\dist\cli.js" (
        echo Building Node.js version...
        cd alive-cli
        call npm run build
        cd ..
    )
)

REM Load previous choice if available
set DEFAULT_CHOICE=0
if exist "%PREFS_FILE%" (
    for /f "tokens=*" %%a in (%PREFS_FILE%) do (
        set DEFAULT_CHOICE=%%a
    )
)

:menu
cls
echo.
echo ===================================
echo        ALIVE AI LAUNCHER
echo ===================================
echo.
if %WSL_AVAILABLE% EQU 1 (
    echo WSL is available on this system.
) else (
    echo WSL is not available. Some options may be limited.
)
echo.
echo Please select which version of Alive AI to run:
echo.
if %RUST_AVAILABLE% EQU 1 (
    echo 1. Run Rust version (alive-rs)
    echo    - Pros: Better performance, more stable
    echo    - Cons: May have fewer features than Node.js version
    echo.
)
if %NODEJS_AVAILABLE% EQU 1 (
    echo 2. Run Node.js version (alive-cli)
    echo    - Pros: Original implementation, may have more features
    echo    - Cons: Potentially less stable, slower performance
    echo.
)
if %WSL_AVAILABLE% EQU 1 (
    if %RUST_AVAILABLE% EQU 1 (
        echo 3. Run Rust version in WSL
        echo    - Pros: Better Linux compatibility
        echo    - Cons: Slightly slower due to WSL overhead
        echo.
    )
    if %NODEJS_AVAILABLE% EQU 1 (
        echo 4. Run Node.js version in WSL
        echo    - Pros: Better Linux compatibility, original implementation
        echo    - Cons: Slower performance due to WSL and Node.js
        echo.
    )
)
echo 0. Exit
echo.
if %DEFAULT_CHOICE% NEQ 0 (
    echo Previous choice: !DEFAULT_CHOICE!
)
echo.
set /p CHOICE="Enter your choice (0-4): "

REM Save choice for next time
if %CHOICE% GEQ 1 if %CHOICE% LEQ 4 (
    echo %CHOICE% > "%PREFS_FILE%"
)

REM Process the choice
if "%CHOICE%"=="0" (
    echo Exiting...
    exit /b 0
) else if "%CHOICE%"=="1" (
    if %RUST_AVAILABLE% EQU 0 (
        echo Error: Rust version is not available.
        goto error
    )
    echo Running Alive AI from Rust version...
    if exist "alive-rs" (
        cd alive-rs
    )
    cargo run --bin alive %*
) else if "%CHOICE%"=="2" (
    if %NODEJS_AVAILABLE% EQU 0 (
        echo Error: Node.js version is not available.
        goto error
    )
    echo Running Alive AI from Node.js version...
    REM Try to run the Node.js version
    cd alive-cli
    node bin/alive.js %PROVIDER_FLAG% %*

    REM If it fails with the cursorOffset error, try the Rust version
    if %ERRORLEVEL% NEQ 0 (
        echo Node.js version failed. Trying Rust version...
        cd ..
        if exist "alive-rs" (
            cd alive-rs
            echo Falling back to Rust version...
            cargo run --bin alive %*
        ) else (
            echo Error: Node.js version failed and Rust version is not available.
            exit /b 1
        )
    )
) else if "%CHOICE%"=="3" (
    if %WSL_AVAILABLE% EQU 0 (
        echo Error: WSL is not available.
        goto error
    )
    if %RUST_AVAILABLE% EQU 0 (
        echo Error: Rust version is not available.
        goto error
    )
    echo Running Alive AI from Rust version in WSL...
    wsl -d Ubuntu bash -c "cd /mnt/c/Users/<USER>/Downloads/Alive\ AI/Alive-AI/alive-rs && cargo run --bin alive"
) else if "%CHOICE%"=="4" (
    if %WSL_AVAILABLE% EQU 0 (
        echo Error: WSL is not available.
        goto error
    )
    if %NODEJS_AVAILABLE% EQU 0 (
        echo Error: Node.js version is not available.
        goto error
    )
    echo Running Alive AI from Node.js version in WSL...
    wsl -d Ubuntu bash -c "export DEEPSEEK_API_KEY='%DEEPSEEK_API_KEY%' && cd /mnt/c/Users/<USER>/Downloads/Alive\ AI/Alive-AI/alive-cli && node bin/alive.js %PROVIDER_FLAG% %*"
) else (
    echo Invalid choice. Please try again.
    timeout /t 2 >nul
    goto menu
)

goto end

:error
echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:end
echo.
echo Alive AI execution completed.
pause
