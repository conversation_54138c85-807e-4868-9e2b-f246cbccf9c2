{
  "compilerOptions": {
    "outDir": "dist",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "target": "esnext",
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ES2022" // Node.js 18
    ],
    "types": ["node", "react"],
    "baseUrl": "./",
    "resolveJsonModule": true, // ESM doesn't yet support JSON modules.
    "jsx": "react",
    "declaration": true,
    "newLine": "lf",
    "stripInternal": true,
    "strict": false,
    "noImplicitReturns": false,
    "noImplicitOverride": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedIndexedAccess": false,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedSideEffectImports": false,
    "noEmitOnError": false,
    "useDefineForClassFields": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "allowJs": true,
    "checkJs": false,
    "noImplicitAny": false
  },
  "include": ["src", "tests", "bin"]
}
