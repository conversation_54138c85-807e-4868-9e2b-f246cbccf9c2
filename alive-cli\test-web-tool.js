// Simple script to test the web tool functionality
import axios from 'axios';

async function testFetch() {
  console.log('Testing Web Tool Functionality...\n');

  // Test basic fetch
  console.log('1. Testing basic fetch...');
  try {
    const response = await axios.get('https://example.com');
    console.log(`Success: true`);
    console.log(`Status: ${response.status}`);
    console.log(`Data length: ${response.data.length} characters`);
  } catch (error) {
    console.log(`Success: false`);
    console.log(`Error: ${error.message}`);
  }
  console.log('\n');

  // Test search with DuckDuckGo
  console.log('2. Testing DuckDuckGo search...');
  try {
    const url = `https://html.duckduckgo.com/html/?q=${encodeURIComponent('Alive AI')}`;
    const response = await axios.get(url);
    console.log(`Success: true`);
    console.log(`Status: ${response.status}`);
    console.log(`Data length: ${response.data.length} characters`);
  } catch (error) {
    console.log(`Success: false`);
    console.log(`Error: ${error.message}`);
  }
  console.log('\n');
}

testFetch().catch(error => {
  console.error('Error running tests:', error);
});
