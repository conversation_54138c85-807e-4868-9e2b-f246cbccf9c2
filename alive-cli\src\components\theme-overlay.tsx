import { Box, Text, useInput } from "ink";
import React, { useState, useEffect } from "react";
import { register<PERSON><PERSON><PERSON><PERSON><PERSON>, unregister<PERSON>c<PERSON><PERSON><PERSON> } from "../utils/terminal";

// Available themes for the application
const AVAILABLE_THEMES = [
  { id: "default", name: "Default" },
  { id: "dark", name: "Dark Mode" },
  { id: "light", name: "Light Mode" },
  { id: "high-contrast", name: "High Contrast" },
  { id: "cool", name: "Cool" },
  { id: "warm", name: "Warm" },
];

// Theme colors and styles for each theme
export const THEME_STYLES = {
  default: {
    border: "blueBright",
    heading: "blueBright",
    text: undefined,
    selected: "greenBright",
    background: undefined,
  },
  dark: {
    border: "blue",
    heading: "cyan",
    text: "white",
    selected: "green",
    background: undefined,
  },
  light: {
    border: "blue",
    heading: "blue",
    text: "black",
    selected: "green",
    background: undefined,
  },
  "high-contrast": {
    border: "yellow",
    heading: "yellow",
    text: "white",
    selected: "yellowBright",
    background: undefined,
  },
  cool: {
    border: "cyan",
    heading: "cyan",
    text: "cyanBright",
    selected: "greenBright",
    background: undefined,
  },
  warm: {
    border: "red",
    heading: "redBright",
    text: "yellow",
    selected: "red",
    background: undefined,
  },
};

/**
 * Overlay component that allows users to change the UI theme
 */
export default function ThemeOverlay({
  onExit,
  onThemeChange,
  currentTheme = "default",
}: {
  onExit: () => void;
  onThemeChange: (themeId: string) => void;
  currentTheme?: string;
}): JSX.Element {
  const [selectedIndex, setSelectedIndex] = useState(() => {
    const current = AVAILABLE_THEMES.findIndex((t) => t.id === currentTheme);
    return current >= 0 ? current : 0;
  });

  // Register ESC handler for this component
  useEffect(() => {
    const handleEsc = () => {
      onExit();
    };
    
    registerEscHandler(handleEsc);
    
    // Clean up on unmount
    return () => {
      unregisterEscHandler(handleEsc);
    };
  }, [onExit]);

  // Handle keyboard input for navigation and selection
  useInput((input, key) => {
    if (key.escape || input === "q") {
      onExit();
      return;
    }

    if (key.upArrow) {
      setSelectedIndex((prev) =>
        prev <= 0 ? AVAILABLE_THEMES.length - 1 : prev - 1
      );
      return;
    }

    if (key.downArrow) {
      setSelectedIndex((prev) =>
        prev >= AVAILABLE_THEMES.length - 1 ? 0 : prev + 1
      );
      return;
    }

    if (key.return) {
      const selectedTheme = AVAILABLE_THEMES[selectedIndex];
      if (selectedTheme) {
        onThemeChange(selectedTheme.id);
        onExit();
      }
      return;
    }
  });

  // Get theme styles for current theme
  const themeStyles = THEME_STYLES[currentTheme as keyof typeof THEME_STYLES] || THEME_STYLES.default;

  return (
    <Box
      flexDirection="column"
      borderStyle="round"
      borderColor={themeStyles.border}
      width={60}
    >
      <Box paddingX={2} paddingY={1} borderStyle="round" borderColor="gray">
        <Text bold color={themeStyles.heading}>Select UI Theme</Text>
      </Box>

      <Box flexDirection="column" paddingX={2} paddingY={1}>
        <Text bold color={themeStyles.text}>Choose a theme for the Alive AI CLI:</Text>
        <Box marginY={1} />
        
        {AVAILABLE_THEMES.map((theme, index) => (
          <Box key={theme.id} marginY={1}>
            <Text color={themeStyles.text}>
              <Text color={selectedIndex === index ? themeStyles.selected : undefined}>
                {selectedIndex === index ? "● " : "○ "}
                <Text bold={selectedIndex === index}>{theme.name}</Text>
              </Text>
            </Text>
          </Box>
        ))}

        <Box marginY={1} />
        <Text dimColor color={themeStyles.text}>
          Use <Text bold>↑/↓</Text> arrows to navigate, <Text bold>Enter</Text> to select, 
          <Text bold> Esc</Text> or <Text bold>q</Text> to cancel
        </Text>
      </Box>
    </Box>
  );
} 