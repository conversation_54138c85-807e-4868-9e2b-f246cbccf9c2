import type { AgentLoop } from "../../utils/agent/agent-loop.js";

import { Box, Text } from "ink";
import path from "node:path";
import React from "react";
import { useTerminalSize } from "../../hooks/use-terminal-size.js";

export interface TerminalHeaderProps {
  terminalRows: number;
  version: string;
  PWD: string;
  model: string;
  provider?: string;
  approvalPolicy: string;
  colorsByPolicy: Record<string, string | undefined>;
  agent?: AgentLoop;
  initialImagePaths?: Array<string>;
  flexModeEnabled?: boolean;
  themeStyles?: {
    border: string;
    heading: string;
    text: string | undefined;
    selected: string;
    background: string | undefined;
  };
}

const TerminalHeader: React.FC<TerminalHeaderProps> = ({
  terminalRows,
  version,
  PWD,
  model,
  provider = "openai",
  approvalPolicy,
  colorsByPolicy,
  agent,
  initialImagePaths,
  flexModeEnabled = false,
  themeStyles,
}) => {
  const { columns } = useTerminalSize();
  const headerWidth = Math.min(columns - 2, 80); // Ensure header doesn't exceed terminal width

  // Get a color for the provider
  const getProviderColor = (providerName: string): string => {
    const providerColors: Record<string, string> = {
      openai: "greenBright",
      deepseek: "blueBright",
      gemini: "yellowBright",
      anthropic: "magentaBright",
      mistral: "cyanBright",
      ollama: "redBright",
      azure: "blueBright",
      openrouter: "yellowBright",
      default: "white",
    };
    return providerColors[providerName.toLowerCase()] || providerColors.default;
  };

  // Get a symbol for the provider
  const getProviderSymbol = (providerName: string): string => {
    const providerSymbols: Record<string, string> = {
      openai: "◆",
      deepseek: "◇",
      gemini: "◈",
      anthropic: "◊",
      mistral: "⬖",
      ollama: "⬗",
      azure: "⬘",
      openrouter: "⬙",
      default: "●",
    };
    return providerSymbols[providerName.toLowerCase()] || providerSymbols.default;
  };

  const providerColor = getProviderColor(provider);
  const providerSymbol = getProviderSymbol(provider);
  
  // Use theme colors if available
  const borderColor = themeStyles?.border || "gray";
  const textColor = themeStyles?.text;
  const headingColor = themeStyles?.heading || "blueBright";

  return (
    <>
      {terminalRows < 10 ? (
        // Compact header for small terminal windows
        <Text color={textColor}>
          <Text color={providerColor}>{providerSymbol}</Text> Alive v{version} - {PWD} - {model} (
          <Text color={providerColor}>{provider}</Text>) -{" "}
          <Text color={colorsByPolicy[approvalPolicy]}>{approvalPolicy}</Text>
          {flexModeEnabled ? " - flex-mode" : ""}
        </Text>
      ) : (
        <>
          <Box
            borderStyle="round"
            borderColor={borderColor}
            paddingX={1}
            width={headerWidth}
            marginBottom={0}
          >
            <Box flexDirection="row" alignItems="center" gap={1}>
              <Text color={providerColor} bold>
                {providerSymbol}
              </Text>
              <Text bold color={textColor || "white"}>AliveAI Alive</Text>
              <Text dimColor color={textColor}>
                (research preview)
              </Text>
              <Text color={headingColor} bold>
                v{version}
              </Text>
            </Box>
          </Box>
          <Box
            borderStyle="round"
            borderColor={borderColor}
            paddingX={1}
            width={headerWidth}
            marginTop={0}
            flexDirection="column"
          >
            <Box flexDirection="row" justifyContent="space-between" width="100%">
              <Text color={textColor}>
                <Text color="gray">session:</Text>{" "}
                <Text color="magentaBright">
                  {agent?.sessionId ? agent.sessionId.substring(0, 8) : "<no-session>"}
                </Text>
              </Text>
              <Text color={colorsByPolicy[approvalPolicy]} bold>
                {approvalPolicy}
              </Text>
            </Box>

            <Box flexDirection="row" marginTop={0}>
              <Box width="50%">
                <Text color={textColor}>
                  <Text color="gray">workdir:</Text>{" "}
                  <Text bold>{PWD.split("/").pop() || PWD}</Text>
                </Text>
              </Box>
              <Box width="50%">
                <Text color={textColor}>
                  <Text color="gray">model:</Text>{" "}
                  <Text bold>{model}</Text>
                </Text>
              </Box>
            </Box>

            <Box flexDirection="row" marginTop={0}>
              <Box width="50%">
                <Text color={textColor}>
                  <Text color="gray">provider:</Text>{" "}
                  <Text color={providerColor} bold>{provider}</Text>
                </Text>
              </Box>
              {flexModeEnabled && (
                <Box width="50%">
                  <Text color={textColor}>
                    <Text color="gray">flex-mode:</Text>{" "}
                    <Text color="greenBright" bold>enabled</Text>
                  </Text>
                </Box>
              )}
            </Box>

            {initialImagePaths && initialImagePaths.length > 0 && (
              <Box marginTop={0}>
                <Text color="gray">
                  images: {initialImagePaths.map(img => path.basename(img)).join(", ")}
                </Text>
              </Box>
            )}
          </Box>
        </>
      )}
    </>
  );
};

export default TerminalHeader;
