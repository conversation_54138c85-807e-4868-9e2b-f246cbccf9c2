# Alive AI for WSL

This package contains Alive AI CLI configured for use in Windows Subsystem for Linux (WSL2).

## Quick Start

1. Make sure you have Node.js 22 or newer installed in your WSL environment:
   ```bash
   node --version
   ```

2. If Node.js is not installed or is an older version, install it:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. Run the installation script:
   ```bash
   ./install.sh
   ```

4. Set your OpenAI API key:
   ```bash
   export OPENAI_API_KEY="your-api-key"
   ```

5. Start using Alive AI:
   ```bash
   alive "Write and run a python program that prints ASCII art"
   ```

## Features

- **Fully Functional AI Assistant**: Get help with coding, debugging, and more
- **Shell Command Execution**: Let the AI run commands to accomplish tasks
- **File Editing**: The AI can create and modify files in your workspace
- **Image Support**: Include images in your prompts with `--image`
- **Session History**: Browse previous sessions with `--history`

## Configuration

Alive AI stores its configuration in `~/.alive-ai/config.toml`. You can customize various settings:

```toml
# Example configuration
model = "alive-mini-latest"
model_provider = "openai"

[sandbox_permissions]
disk_write_cwd = true
disk_write_tmp = true
network = true
```

## Command Reference

- Get help: `alive --help`
- Use a specific model: `alive --model <model-name> "your prompt"`
- Include an image: `alive --image /path/to/image.png "describe this image"`
- Browse previous sessions: `alive --history`
- Configure approval mode: `alive --ask-for-approval always "run some commands"`

## Troubleshooting

If you encounter any issues:

1. Verify your API key is set correctly
2. Check your internet connection
3. Make sure you're using Node.js 22 or newer
4. Look for error messages in the output

For more detailed information, see the full documentation at [https://github.com/alive-ai/alive-ai](https://github.com/alive-ai/alive-ai).

## License

Apache License 2.0
