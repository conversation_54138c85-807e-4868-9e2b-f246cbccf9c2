# Alive AI Project Documentation

This document provides guidance for AI assistants interacting with the Alive AI codebase.

## Project Overview

Alive AI is a lightweight AI coding assistant that runs in your terminal. It allows developers to:

1. Interact with an AI assistant through a command-line interface
2. Execute commands in a sandboxed environment
3. Make changes to files with appropriate permissions
4. Understand and navigate codebases efficiently

## Architecture

The project consists of two main components:

1. **TypeScript/JavaScript Frontend (alive-cli)**: Handles user interaction, command parsing, and display
2. **Rust Backend (alive-rs)**: Provides sandboxing, security, and core functionality

### Key Components

- **CLI Interface**: Processes user input and displays AI responses
- **Model Context Protocol (MCP)**: Standardized way for models to interact with tools
- **Sandboxing**: Platform-specific security mechanisms to safely execute commands
- **Tool System**: Allows the AI to perform actions like running shell commands

## Development Guidelines

When working with this codebase:

1. **Security First**: Always consider security implications of changes
2. **Cross-Platform**: Ensure compatibility with macOS, Linux, and Windows (via WSL2)
3. **Performance**: Be mindful of resource usage, especially for terminal applications
4. **User Experience**: Maintain a clean, intuitive interface

## Common Tasks

### Adding a New Tool

1. Define the tool interface in `alive-rs/mcp-types/src/lib.rs`
2. Implement the tool in `alive-rs/core/src/`
3. Register the tool in `alive-rs/core/src/client.rs`
4. Add TypeScript bindings in `alive-cli/src/utils/tools/`

### Modifying the CLI

1. Main CLI entry point is in `alive-cli/src/cli.tsx`
2. Command parsing is handled in `alive-cli/src/utils/commands.ts`
3. UI components are in `alive-cli/src/components/`

### Testing

- TypeScript tests use Vitest
- Rust tests use the standard Rust testing framework
- Run tests with `pnpm test` in the CLI directory

## Important Notes

- Never add or modify any code related to `ALIVE_SANDBOX_NETWORK_DISABLED_ENV_VAR`. You operate in a sandbox where `ALIVE_SANDBOX_NETWORK_DISABLED=1` will be set whenever you use the `shell` tool. Any existing code that uses these environment variables was authored with this fact in mind. It is often used to early exit out of tests that the author knew you would not be able to run given your sandbox limitations.
