import { log } from "../../utils/logger/log.js";
import { Box, Text, useInput, useStdin } from "ink";
import React, { useState, useEffect } from "react";
import { useInterval } from "../../utils/hooks/use-interval.js";
import { Action, actionTracker } from "../../utils/action-tracker.js";
import { useTerminalSize } from "../../hooks/use-terminal-size.js";

// Enhanced thinking display that shows current and upcoming actions
// while preserving the original elapsed-time counter functionality

export default function TerminalChatInputThinking({
  onInterrupt,
  active,
  thinkingSeconds,
}: {
  onInterrupt: () => void;
  active: boolean;
  thinkingSeconds: number;
}): React.ReactElement {
  const [awaitingConfirm, setAwaitingConfirm] = useState(false);
  const [currentAction, setCurrentAction] = useState<Action | null>(null);
  const [upcomingActions, setUpcomingActions] = useState<Action[]>([]);
  const [progressBar, setProgressBar] = useState<string>("");
  const [progressPercent, setProgressPercent] = useState<number>(0);
  const { columns } = useTerminalSize();
  const boxWidth = Math.min(columns - 2, 80); // Ensure box doesn't exceed terminal width

  const { stdin, setRawMode } = useStdin();

  React.useEffect(() => {
    if (!active) {
      return;
    }

    setRawMode?.(true);

    const onData = (data: Buffer | string) => {
      if (awaitingConfirm) {
        return;
      }

      const str = Buffer.isBuffer(data) ? data.toString("utf8") : data;
      if (str === "\x1b\x1b") {
        log(
          "raw stdin: received collapsed ESC ESC – starting confirmation timer",
        );
        setAwaitingConfirm(true);
        setTimeout(() => setAwaitingConfirm(false), 1500);
      }
    };

    stdin?.on("data", onData);
    return () => {
      stdin?.off("data", onData);
    };
  }, [stdin, awaitingConfirm, onInterrupt, active, setRawMode]);

  // Listen for action updates from the ActionTracker
  useEffect(() => {
    if (!active) {
      return;
    }

    // Update state when actions change
    const handleActionsUpdated = () => {
      setCurrentAction(actionTracker.getCurrentAction());
      setUpcomingActions(actionTracker.getUpcomingActions());
    };

    // Update progress bar when progress changes
    const handleProgressUpdated = (action: Action) => {
      if (action.progress !== undefined) {
        setProgressPercent(action.progress);

        // Create a visual progress bar
        const barLength = 20;
        const filledLength = Math.floor((action.progress / 100) * barLength);
        const emptyLength = barLength - filledLength;

        setProgressBar(
          '█'.repeat(filledLength) + '░'.repeat(emptyLength) +
          ` ${action.progress}%`
        );
      }
    };

    // Handle action started event
    const handleActionStarted = (action: Action) => {
      setCurrentAction(action);

      // If there's no progress yet, set an initial progress
      if (action.progress === undefined) {
        // For thinking actions, start at 5%
        if (action.type === 'thinking') {
          actionTracker.updateProgress(5);
          actionTracker.updateCurrentActionDetails({
            progressMessage: 'Starting...'
          });
        } else {
          actionTracker.updateProgress(0);
        }
      }
    };

    // Handle action completed event
    const handleActionCompleted = (action: Action) => {
      // If there are no more actions, clear the current action
      if (actionTracker.getUpcomingActions().length === 0) {
        setCurrentAction(null);
      }
    };

    // Subscribe to events
    actionTracker.on('actions-updated', handleActionsUpdated);
    actionTracker.on('progress-updated', handleProgressUpdated);
    actionTracker.on('action-started', handleActionStarted);
    actionTracker.on('action-completed', handleActionCompleted);
    actionTracker.on('subaction-added', handleActionsUpdated);

    // Initial state
    handleActionsUpdated();

    // Cleanup
    return () => {
      actionTracker.off('actions-updated', handleActionsUpdated);
      actionTracker.off('progress-updated', handleProgressUpdated);
      actionTracker.off('action-started', handleActionStarted);
      actionTracker.off('action-completed', handleActionCompleted);
      actionTracker.off('subaction-added', handleActionsUpdated);
    };
  }, [active]);

  useInput(
    (_input, key) => {
      if (!key.escape) {
        return;
      }

      if (awaitingConfirm) {
        log("useInput: second ESC detected – triggering onInterrupt()");
        onInterrupt();
        setAwaitingConfirm(false);
      } else {
        log("useInput: first ESC detected – waiting for confirmation");
        setAwaitingConfirm(true);
        setTimeout(() => setAwaitingConfirm(false), 1500);
      }
    },
    { isActive: active },
  );

  // Custom ball animation including the elapsed seconds
  const ballFrames = [
    "( ●    )",
    "(  ●   )",
    "(   ●  )",
    "(    ● )",
    "(     ●)",
    "(    ● )",
    "(   ●  )",
    "(  ●   )",
    "( ●    )",
    "(●     )",
  ];

  const [frame, setFrame] = useState(0);

  useInterval(() => {
    setFrame((idx) => (idx + 1) % ballFrames.length);
  }, 80);

  // Preserve the spinner (ball) animation while keeping the elapsed seconds
  // text static.  We achieve this by rendering the bouncing ball inside the
  // parentheses and appending the seconds counter *after* the spinner rather
  // than injecting it directly next to the ball (which caused the counter to
  // move horizontally together with the ball).

  const frameTemplate = ballFrames[frame] ?? ballFrames[0];
  const frameWithSeconds = `${frameTemplate} ${thinkingSeconds}s`;

  // Helper function to get color based on action type
  const getActionTypeColor = (type: Action['type']) => {
    switch (type) {
      case 'file_read': return 'blueBright';
      case 'file_write': return 'greenBright';
      case 'network': return 'magentaBright';
      case 'execution': return 'yellowBright';
      case 'thinking': return 'cyanBright';
      default: return 'white';
    }
  };

  // Helper function to get icon based on action type
  const getActionTypeIcon = (type: Action['type']) => {
    switch (type) {
      case 'file_read': return '📖';
      case 'file_write': return '💾';
      case 'network': return '🌐';
      case 'execution': return '⚙️';
      case 'thinking': return '🧠';
      case 'processing': return '🔄';
      default: return '•';
    }
  };

  // Enhanced spinner frames with more visual appeal
  const spinnerFrames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
  const [spinnerFrame, setSpinnerFrame] = useState(0);

  // Animate the spinner
  useInterval(() => {
    setSpinnerFrame((prev) => (prev + 1) % spinnerFrames.length);
  }, 100);

  // Calculate elapsed time in a more readable format
  const formatElapsedTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Determine status message based on current action
  const getStatusMessage = (): string => {
    if (!currentAction) return "Thinking...";

    switch (currentAction.type) {
      case 'thinking': return "Processing your request...";
      case 'file_read': return "Reading files...";
      case 'file_write': return "Writing to files...";
      case 'execution': return "Executing command...";
      case 'network': return "Fetching data...";
      default: return "Working...";
    }
  };

  return (
    <Box width={boxWidth} flexDirection="column" borderStyle="round" borderColor="gray">
      <Box flexDirection="row" justifyContent="space-between" alignItems="center" paddingX={1}>
        <Box>
          <Text color="blueBright" bold>
            {spinnerFrames[spinnerFrame]}  Thinking{thinkingSeconds > 0 ? "..." : ""}
          </Text>
          <Text color="normal">{thinkingSeconds > 0 ? "" : "..."}</Text>
        </Box>
        <Box>
          <Text color="yellow" bold>
            {formatElapsedTime(thinkingSeconds)}
          </Text>
        </Box>
      </Box>

      <Box flexDirection="column" paddingX={1} paddingY={1}>
        <Box>
          <Text color="gray">{progressBar}</Text>
        </Box>
        <Box paddingTop={1} justifyContent="space-between" flexDirection="row">
          <Text dimColor>{getStatusMessage()}</Text>
          <Text>
            <Text dimColor>press</Text> <Text bold color="redBright">Esc</Text>{" "}
            {awaitingConfirm ? (
              <Text bold color="redBright">again</Text>
            ) : (
              <Text dimColor>twice</Text>
            )}{" "}
            <Text dimColor>to interrupt</Text>
          </Text>
        </Box>
      </Box>

      {currentAction && (
        <Box flexDirection="column" paddingX={1} paddingY={1} borderStyle="single" borderColor="gray">
          <Text bold color="blueBright">Current Action</Text>
          <Box paddingY={1}>
            <Text color={getActionTypeColor(currentAction.type)} bold>
              {getActionTypeIcon(currentAction.type)} {currentAction.description}
            </Text>
            {currentAction.details?.estimatedTimeRemaining !== undefined && (
              <Text color="yellow" dimColor>
                {' '}(~{currentAction.details.estimatedTimeRemaining}s remaining)
              </Text>
            )}
          </Box>
          {currentAction.details?.path && (
            <Box paddingLeft={1}>
              <Text dimColor>
                Path: {currentAction.details.filename || currentAction.details.path}
              </Text>
            </Box>
          )}
          {currentAction.details?.error && (
            <Box paddingLeft={1}>
              <Text color="redBright">Error: {currentAction.details.error}</Text>
            </Box>
          )}
          {currentAction.details?.command && (
            <Box paddingLeft={1}>
              <Text dimColor>Command: {currentAction.details.command}</Text>
            </Box>
          )}
          
          {currentAction.progress && (
            <Box paddingTop={1}>
              <Text color="gray">Progress: {currentAction.progress}%</Text>
            </Box>
          )}
          
          {currentAction.details?.progressMessage && (
            <Box paddingTop={1}>
              <Text color="cyan" italic>
                {currentAction.details.progressMessage}
              </Text>
            </Box>
          )}
          
          {currentAction.subActions && currentAction.subActions.length > 0 && (
            <Box flexDirection="column" paddingY={1}>
              <Text bold>Sub-tasks:</Text>
              {currentAction.subActions.slice(0, 2).map((subAction: any) => (
                <Box key={subAction.id} paddingLeft={1}>
                  <Text color={getActionTypeColor(subAction.type)} dimColor>
                    • {subAction.description}
                  </Text>
                </Box>
              ))}
              {currentAction.subActions.length > 2 && (
                <Box paddingLeft={1}>
                  <Text dimColor>...and {currentAction.subActions.length - 2} more sub-tasks</Text>
                </Box>
              )}
            </Box>
          )}
        </Box>
      )}

      {/* Upcoming actions with enhanced visualization */}
      {upcomingActions.length > 0 && (
        <Box flexDirection="column" paddingX={1} paddingY={1} borderStyle="single" borderColor="gray">
          <Text bold>Upcoming Actions:</Text>
          {upcomingActions.slice(0, 3).map((action, index) => (
            <Box key={action.id} paddingLeft={1} paddingTop={0}>
              <Text color={getActionTypeColor(action.type)}>
                {index + 1}. {getActionTypeIcon(action.type)} {action.description}
              </Text>
              {action.details?.path && (
                <Box paddingLeft={3}>
                  <Text dimColor>
                    Path: {action.details.filename || action.details.path}
                  </Text>
                </Box>
              )}
              {action.details?.command && (
                <Box paddingLeft={3}>
                  <Text dimColor>Command: {action.details.command}</Text>
                </Box>
              )}
            </Box>
          ))}
          {upcomingActions.length > 3 && (
            <Box paddingLeft={1} paddingTop={0}>
              <Text dimColor>...and {upcomingActions.length - 3} more actions in queue</Text>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
}
