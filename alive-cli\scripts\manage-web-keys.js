#!/usr/bin/env node
import { promises as fs } from 'fs';
import path from 'path';
import { homedir } from 'os';
import readline from 'readline';

// Function to create a prompt interface
function createPrompt() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

// Function to prompt for input
async function promptInput(question) {
  const rl = createPrompt();
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Function to mask sensitive input (like API keys)
async function promptMaskedInput(question) {
  process.stdout.write(question);

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  // Make password entry silent
  rl.stdoutMuted = true;

  // Override _writeToOutput to mask characters
  rl._writeToOutput = function _writeToOutput(stringToWrite) {
    if (rl.stdoutMuted && stringToWrite !== '\n') {
      rl.output.write('*');
    } else {
      rl.output.write(stringToWrite);
    }
  };

  return new Promise((resolve) => {
    rl.question('', (answer) => {
      process.stdout.write('\n');
      rl.close();
      resolve(answer);
    });
  });
}

// Function to load the auth.json file
async function loadAuthFile() {
  const authDir = path.join(homedir(), '.alive-ai');
  const authFile = path.join(authDir, 'auth.json');

  try {
    await fs.mkdir(authDir, { recursive: true, mode: 0o700 });

    try {
      const data = await fs.readFile(authFile, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return {}; // File doesn't exist yet
      }
      throw error;
    }
  } catch (error) {
    console.error('Error loading auth file:', error);
    return {};
  }
}

// Function to save the auth.json file
async function saveAuthFile(data) {
  const authDir = path.join(homedir(), '.alive-ai');
  const authFile = path.join(authDir, 'auth.json');

  try {
    await fs.mkdir(authDir, { recursive: true, mode: 0o700 });
    await fs.writeFile(authFile, JSON.stringify(data, null, 2), { mode: 0o600 });
    console.log('API keys saved successfully to auth.json!');
  } catch (error) {
    console.error('Error saving auth file:', error);
  }
}

// Function to load the config.json file
async function loadConfigFile() {
  const configDir = path.join(homedir(), '.alive-ai');
  const configFile = path.join(configDir, 'config.json');

  try {
    await fs.mkdir(configDir, { recursive: true, mode: 0o700 });

    try {
      const data = await fs.readFile(configFile, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return {}; // File doesn't exist yet
      }
      throw error;
    }
  } catch (error) {
    console.error('Error loading config file:', error);
    return {};
  }
}

// Function to save the config.json file
async function saveConfigFile(data) {
  const configDir = path.join(homedir(), '.alive-ai');
  const configFile = path.join(configDir, 'config.json');

  try {
    await fs.mkdir(configDir, { recursive: true, mode: 0o700 });
    await fs.writeFile(configFile, JSON.stringify(data, null, 2), { mode: 0o600 });
    console.log('Config updated successfully!');
  } catch (error) {
    console.error('Error saving config file:', error);
  }
}

// Function to display current API key settings
async function showCurrentKeys() {
  const authData = await loadAuthFile();
  const configData = await loadConfigFile();

  console.log('\n=== Current Web Tool API Key Settings ===');

  // Get web tool config from auth.json
  const authWebTool = authData.webTool || {};

  // Get web tool config from config.json
  const configWebTool = configData.webTool || {};

  // Merge settings (auth.json takes precedence)
  const webTool = {
    ...configWebTool,
    ...authWebTool
  };

  console.log('Google API Key: ' + (webTool.googleApiKey ? '********' : 'Not set'));
  console.log('Google Custom Search Engine ID: ' + (webTool.googleSearchCx || 'Not set'));
  console.log('Bing API Key: ' + (webTool.bingApiKey ? '********' : 'Not set'));

  // Display the default search provider with more details
  let providerInfo = webTool.defaultSearchProvider || 'duckduckgo';

  // Add notes about API key requirements
  switch (providerInfo) {
    case 'google':
      providerInfo += ' (requires Google API Key and Custom Search Engine ID)';
      break;
    case 'bing':
      providerInfo += ' (requires Bing API Key)';
      break;
    case 'stackoverflow':
      providerInfo += ' (no API key required)';
      break;
    case 'github':
      providerInfo += ' (no API key required)';
      break;
    case 'duckduckgo':
      providerInfo += ' (no API key required)';
      break;
  }

  console.log('Default Search Provider: ' + providerInfo);
  console.log('');
}

// Function to update API keys
async function updateApiKeys() {
  const authData = await loadAuthFile();
  const configData = await loadConfigFile();

  authData.webTool = authData.webTool || {};
  configData.webTool = configData.webTool || {};
  configData.enableWebTool = true;

  console.log('\nUpdating Web Tool API Keys');
  console.log('(press Enter to keep current value)\n');

  // Google API Key
  const googleKeyPrompt = `Google API Key${authData.webTool.googleApiKey ? ' [current: ********]' : ''}: `;
  const googleApiKey = await promptMaskedInput(googleKeyPrompt);

  if (googleApiKey.trim()) {
    authData.webTool.googleApiKey = googleApiKey;
    configData.webTool.googleApiKey = googleApiKey;
    process.env.GOOGLE_API_KEY = googleApiKey;
  }

  // Google Custom Search Engine ID
  const googleCxPrompt = `Google Custom Search Engine ID${authData.webTool.googleSearchCx ? ' [current: ' + authData.webTool.googleSearchCx + ']' : ''}: `;
  const googleSearchCx = await promptInput(googleCxPrompt);

  if (googleSearchCx.trim()) {
    authData.webTool.googleSearchCx = googleSearchCx;
    configData.webTool.googleSearchCx = googleSearchCx;
    process.env.GOOGLE_SEARCH_CX = googleSearchCx;
  }

  // Bing API Key
  const bingKeyPrompt = `Bing API Key${authData.webTool.bingApiKey ? ' [current: ********]' : ''}: `;
  const bingApiKey = await promptMaskedInput(bingKeyPrompt);

  if (bingApiKey.trim()) {
    authData.webTool.bingApiKey = bingApiKey;
    configData.webTool.bingApiKey = bingApiKey;
    process.env.BING_API_KEY = bingApiKey;
  }

  // Default Search Provider
  const currentProvider = authData.webTool.defaultSearchProvider || 'duckduckgo';
  console.log('\nSelect default search provider:');
  console.log(`1. Google ${currentProvider === 'google' ? '(current)' : ''}`);
  console.log(`2. Bing ${currentProvider === 'bing' ? '(current)' : ''}`);
  console.log(`3. DuckDuckGo ${currentProvider === 'duckduckgo' ? '(current)' : ''}`);
  console.log(`4. StackOverflow ${currentProvider === 'stackoverflow' ? '(current)' : ''}`);
  console.log(`5. GitHub ${currentProvider === 'github' ? '(current)' : ''}`);

  const providerChoice = await promptInput('\nChoice (1-5): ');

  let defaultSearchProvider = currentProvider;

  switch (providerChoice.trim()) {
    case '1':
      defaultSearchProvider = 'google';
      break;
    case '2':
      defaultSearchProvider = 'bing';
      break;
    case '3':
      defaultSearchProvider = 'duckduckgo';
      break;
    case '4':
      defaultSearchProvider = 'stackoverflow';
      break;
    case '5':
      defaultSearchProvider = 'github';
      break;
  }

  authData.webTool.defaultSearchProvider = defaultSearchProvider;
  configData.webTool.defaultSearchProvider = defaultSearchProvider;
  process.env.ALIVE_DEFAULT_SEARCH_PROVIDER = defaultSearchProvider;

  // Save the changes
  await saveAuthFile(authData);
  await saveConfigFile(configData);

  console.log('\nAPI keys updated successfully!');
}

// Function to clear API keys
async function clearApiKeys() {
  const confirm = await promptInput('\nAre you sure you want to clear all web tool API keys? (yes/no): ');

  if (confirm.toLowerCase() !== 'yes') {
    console.log('Operation canceled.');
    return;
  }

  const authData = await loadAuthFile();
  const configData = await loadConfigFile();

  // Clear keys in auth.json
  if (authData.webTool) {
    delete authData.webTool.googleApiKey;
    delete authData.webTool.googleSearchCx;
    delete authData.webTool.bingApiKey;
    authData.webTool.defaultSearchProvider = 'duckduckgo';
  }

  // Clear keys in config.json
  if (configData.webTool) {
    delete configData.webTool.googleApiKey;
    delete configData.webTool.googleSearchCx;
    delete configData.webTool.bingApiKey;
    configData.webTool.defaultSearchProvider = 'duckduckgo';
  }

  // Save the changes
  await saveAuthFile(authData);
  await saveConfigFile(configData);

  console.log('All web tool API keys have been cleared.');
}

// Main function
async function main() {
  console.log('=== Alive AI Web Tool API Key Manager ===');

  while (true) {
    await showCurrentKeys();

    console.log('Select an option:');
    console.log('1. Update API keys');
    console.log('2. Clear API keys');
    console.log('3. Exit');

    const choice = await promptInput('\nChoice (1-3): ');

    switch (choice.trim()) {
      case '1':
        await updateApiKeys();
        break;
      case '2':
        await clearApiKeys();
        break;
      case '3':
        console.log('Exiting...');
        return;
      default:
        console.log('Invalid option. Please try again.');
    }
  }
}

// Run the script
main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});