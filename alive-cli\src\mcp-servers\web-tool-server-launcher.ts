#!/usr/bin/env node
/**
 * Web Tool MCP Server Launcher
 *
 * This script launches the Web Tool MCP Server as a standalone process.
 * It can be used by Alive AI to start the server when needed.
 *
 * IMPORTANT: This file should NOT import any React-related code
 * to avoid React hook errors.
 */

import { startWebToolServer } from './web-tool-server';
import { getWebToolKeys } from '../utils/web-tool-keys';

// Suppress all React-related errors and warnings
const originalConsoleError = console.error;
console.error = function(...args: any[]) {
  const errorMessage = args[0]?.toString() || '';
  if (errorMessage.includes('React') ||
      errorMessage.includes('hook') ||
      errorMessage.includes('useState') ||
      errorMessage.includes('Cannot read properties of null') ||
      errorMessage.includes('Invalid hook call') ||
      errorMessage.includes('object null is not iterable') ||
      errorMessage.includes('Hooks can only be called inside')) {
    // Suppress React-related errors
    return;
  }

  // Pass through all other errors
  return originalConsoleError.apply(console, args);
};

// Load web tool keys from environment variables or config
const webToolKeys = getWebToolKeys();

// Start the server
try {
  startWebToolServer(webToolKeys);
  console.log('Web Tool MCP Server launched');
} catch (error) {
  console.error('Failed to start Web Tool MCP Server:', error);
  process.exit(1);
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('Web Tool MCP Server shutting down (SIGINT)');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Web Tool MCP Server shutting down (SIGTERM)');
  process.exit(0);
});
