import { Agent<PERSON>oop } from "../src/utils/agent/agent-loop";
import { ResponseItem } from "openai/resources/responses/responses";
import { ReviewDecision } from "../src/utils/agent/review";
import { AppConfig } from "../src/utils/config";
import { vi, describe, beforeEach, afterEach, it, expect } from "vitest";
import { mkdirSync, writeFileSync, readFileSync, unlinkSync, rmdirSync } from "fs";
import path from "path";
import os from "os";

const TEST_DIR = path.join(os.tmpdir(), "alive-file-tool-test");

describe("AgentLoop file tool", () => {
  const items: Array<ResponseItem> = [];
  let loading = false;
  let lastResponseId = "";
  const config: AppConfig = {
    model: "test-model",
    instructions: "test instructions",
    apiKey: "test-api-key",
  };

  let agent: AgentLoop;

  beforeEach(() => {
    items.length = 0;
    loading = false;
    lastResponseId = "";

    // Create test directory
    mkdirSync(TEST_DIR, { recursive: true });
    
    agent = new AgentLoop({
      model: "test-model",
      instructions: "test instructions",
      approvalPolicy: "auto-edit",
      onItem: (item) => items.push(item),
      onLoading: (l) => (loading = l),
      getCommandConfirmation: async () => ({ review: ReviewDecision.YES }),
      onLastResponseId: (id) => (lastResponseId = id),
      additionalWritableRoots: [TEST_DIR],
      config,
    });

    // Mock the OpenAI client to avoid actual network calls
    vi.spyOn(agent as any, "oai").mockImplementation({
      responses: {
        create: vi.fn().mockImplementation(async () => {
          return {
            id: "resp_abc123",
            created_at: Date.now(),
            completed_at: Date.now(),
            status: "completed",
            model: "test-model",
            output: [],
          };
        }),
      },
    });
  });

  afterEach(() => {
    try {
      // Clean up test files
      rmdirSync(TEST_DIR, { recursive: true });
    } catch (error) {
      console.error("Error cleaning up test directory:", error);
    }
  });

  it("handles a file read operation", async () => {
    // Create a test file
    const testFilePath = path.join(TEST_DIR, "test-file.txt");
    const testContent = "This is test content";
    writeFileSync(testFilePath, testContent);

    // Create a file tool call to read the file
    const functionCall = {
      id: "call_123",
      type: "function" as const,
      function: {
        name: "file",
        arguments: JSON.stringify({
          operation: "read",
          path: testFilePath,
        }),
      },
    };

    // Process the function call directly
    const output = await (agent as any).handleFunctionCall(functionCall);
    
    // Verify the result
    expect(output).toHaveLength(1);
    expect(output[0].type).toBe("function_call_output");
    expect(output[0].call_id).toBe("call_123");
    
    // Parse the JSON output
    const result = JSON.parse(output[0].output);
    expect(result.output).toBe(testContent);
    expect(result.success).toBe(true);
  });

  it("handles a file write operation", async () => {
    // Create a file tool call to write a file
    const testFilePath = path.join(TEST_DIR, "write-test.txt");
    const testContent = "This is written content";
    
    const functionCall = {
      id: "call_456",
      type: "function" as const,
      function: {
        name: "file",
        arguments: JSON.stringify({
          operation: "write",
          path: testFilePath,
          content: testContent,
        }),
      },
    };

    // Process the function call directly
    const output = await (agent as any).handleFunctionCall(functionCall);
    
    // Verify the result
    expect(output).toHaveLength(1);
    expect(output[0].type).toBe("function_call_output");
    expect(output[0].call_id).toBe("call_456");
    
    // Parse the JSON output
    const result = JSON.parse(output[0].output);
    expect(result.success).toBe(true);
    
    // Verify the file was actually written
    const actualContent = readFileSync(testFilePath, 'utf8');
    expect(actualContent).toBe(testContent);
  });

  it("handles a file append operation", async () => {
    // Create a test file with initial content
    const testFilePath = path.join(TEST_DIR, "append-test.txt");
    const initialContent = "Initial content\n";
    const appendContent = "Appended content";
    writeFileSync(testFilePath, initialContent);
    
    const functionCall = {
      id: "call_789",
      type: "function" as const,
      function: {
        name: "file",
        arguments: JSON.stringify({
          operation: "append",
          path: testFilePath,
          content: appendContent,
        }),
      },
    };

    // Process the function call directly
    const output = await (agent as any).handleFunctionCall(functionCall);
    
    // Verify the result
    expect(output).toHaveLength(1);
    expect(output[0].type).toBe("function_call_output");
    
    // Parse the JSON output
    const result = JSON.parse(output[0].output);
    expect(result.success).toBe(true);
    
    // Verify the file was actually appended
    const actualContent = readFileSync(testFilePath, 'utf8');
    expect(actualContent).toBe(initialContent + appendContent);
  });

  it("handles a file delete operation", async () => {
    // Create a test file
    const testFilePath = path.join(TEST_DIR, "delete-test.txt");
    writeFileSync(testFilePath, "Delete me");
    
    const functionCall = {
      id: "call_delete",
      type: "function" as const,
      function: {
        name: "file",
        arguments: JSON.stringify({
          operation: "delete",
          path: testFilePath,
        }),
      },
    };

    // Process the function call directly
    const output = await (agent as any).handleFunctionCall(functionCall);
    
    // Verify the result
    expect(output).toHaveLength(1);
    expect(output[0].type).toBe("function_call_output");
    
    // Parse the JSON output
    const result = JSON.parse(output[0].output);
    expect(result.success).toBe(true);
    
    // Verify the file was actually deleted
    let fileExists = true;
    try {
      readFileSync(testFilePath);
    } catch (error) {
      fileExists = false;
    }
    expect(fileExists).toBe(false);
  });

  it("handles errors gracefully", async () => {
    // Try to read a non-existent file
    const nonExistentPath = path.join(TEST_DIR, "does-not-exist.txt");
    
    const functionCall = {
      id: "call_error",
      type: "function" as const,
      function: {
        name: "file",
        arguments: JSON.stringify({
          operation: "read",
          path: nonExistentPath,
        }),
      },
    };

    // Process the function call directly
    const output = await (agent as any).handleFunctionCall(functionCall);
    
    // Verify the error handling
    expect(output).toHaveLength(1);
    expect(output[0].type).toBe("function_call_output");
    
    // Parse the JSON output
    const result = JSON.parse(output[0].output);
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
}); 