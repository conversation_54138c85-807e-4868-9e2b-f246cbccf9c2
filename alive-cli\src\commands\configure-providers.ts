import { providers } from "../utils/providers";
import chalk from "chalk";
import fs from "fs";
import path from "path";
import os from "os";
import { log } from "../utils/logger/log";
import prompts from "prompts";

interface ProviderConfig {
  apiKey?: string;
  name: string;
  baseURL: string;
  envKey: string;
}

interface AuthData {
  [key: string]: any;
  providerKeys?: Record<string, string>;
  last_refresh?: string;
}

/**
 * Command to configure API keys for different providers
 */
export async function configureProvidersCommand(): Promise<void> {
  // eslint-disable-next-line no-console
  console.log(chalk.bold("\n🔑 Provider API Key Configuration\n"));
  
  // Get available providers
  const availableProviders = Object.entries(providers)
    .filter(([id, _]) => id !== "ollama") // Skip Ollama as it doesn't need an API key
    .map(([id, info]) => ({
      id,
      name: info.name,
      envKey: info.envKey,
      baseURL: info.baseURL,
      apiKey: process.env[info.envKey]
    }));
  
  // Display current configuration
  // eslint-disable-next-line no-console
  console.log(chalk.bold("Current provider configuration:"));
  for (const provider of availableProviders) {
    const status = provider.apiKey ? chalk.green("✓ Configured") : chalk.red("✗ Not configured");
    // eslint-disable-next-line no-console
    console.log(`  ${chalk.bold(provider.name)} (${provider.id}): ${status}`);
  }
  
  // eslint-disable-next-line no-console
  console.log("\n");

  // Ask user which provider to configure
  const { selectedProvider } = await prompts({
    type: "select",
    name: "selectedProvider",
    message: "Which provider would you like to configure?",
    choices: availableProviders.map(p => ({ title: p.name, value: p.id }))
  });

  if (!selectedProvider) {
    // User cancelled
    return;
  }

  const provider = availableProviders.find(p => p.id === selectedProvider);
  if (!provider) {
    // eslint-disable-next-line no-console
    console.error(chalk.red(`Provider ${selectedProvider} not found.`));
    return;
  }

  // Ask for API key
  const { apiKey } = await prompts({
    type: "password",
    name: "apiKey",
    message: `Enter API key for ${provider.name}:`,
    initial: provider.apiKey || ""
  });

  if (!apiKey || apiKey.trim() === "") {
    // eslint-disable-next-line no-console
    console.log(chalk.yellow("No API key provided. Configuration cancelled."));
    return;
  }

  // Save to auth.json file
  try {
    const home = os.homedir();
    const authDir = path.join(home, ".alive-ai");
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(authDir)) {
      fs.mkdirSync(authDir, { recursive: true, mode: 0o700 });
    }
    
    const authFile = path.join(authDir, "auth.json");
    let authData: AuthData = {};
    
    // Load existing data if file exists
    if (fs.existsSync(authFile)) {
      authData = JSON.parse(fs.readFileSync(authFile, "utf-8"));
    }
    
    // Initialize provider keys if not exists
    if (!authData.providerKeys) {
      authData.providerKeys = {};
    }
    
    // Set the API key
    authData.providerKeys[provider.id] = apiKey;
    // Also set as environment variable for current session
    process.env[provider.envKey] = apiKey;
    
    // Update timestamp
    authData.last_refresh = new Date().toISOString();
    
    // Write to file
    fs.writeFileSync(authFile, JSON.stringify(authData, null, 2), { mode: 0o600 });
    
    // eslint-disable-next-line no-console
    console.log(chalk.green(`✓ Successfully configured ${provider.name} API key.`));
    // eslint-disable-next-line no-console
    console.log(`\nTo use this provider, run:\n  ${chalk.cyan(`alive --provider ${provider.id} <your-prompt>`)}`);
    
    log(`Configured ${provider.name} API key in auth.json and set ${provider.envKey} environment variable`);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(chalk.red(`Failed to save API key: ${error}`));
  }
} 