import * as path from 'path';

/**
 * Validates that a path string actually looks like a path
 * This helps prevent using AI text as if it were a file path
 * 
 * @param pathStr The path string to validate
 * @returns True if the path looks like a valid file path
 */
export function isValidPathString(pathStr: string): boolean {
  if (!pathStr || typeof pathStr !== 'string') {
    return false;
  }

  // Path must be either absolute or contain path separators
  const isAbsolute = path.isAbsolute(pathStr);
  
  // Check if the path has reasonable path components
  const hasPathSeparators = pathStr.includes('/') || pathStr.includes('\\');
  
  // Check for patterns that suggest this isn't a file path
  const hasQuestionMark = pathStr.includes('?');
  const isUrl = pathStr.includes('://');
  const hasNewlines = pathStr.includes('\n');
  const isTooLong = pathStr.length > 500; // Most paths aren't extremely long

  return (isAbsolute || hasPathSeparators) && 
         !hasQuestionMark && 
         !isUrl && 
         !hasNewlines &&
         !isTooLong;
}

/**
 * Sanitizes a path string by validating it first
 * 
 * @param pathStr The path string to sanitize
 * @returns The path if valid, or null if invalid
 */
export function sanitizePath(pathStr: string | undefined | null): string | null {
  if (!pathStr) {
    return null;
  }
  
  return isValidPathString(pathStr) ? pathStr : null;
} 