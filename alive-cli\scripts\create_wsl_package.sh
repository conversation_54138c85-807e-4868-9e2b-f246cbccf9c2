#!/bin/bash
# Script to create a package for installation in Windows 11 WSL
# This script creates a tarball that can be installed in WSL

set -euo pipefail

# Create a temporary directory for staging the release
TMPDIR=$(mktemp -d)
trap "rm -rf $TMPDIR" EXIT

echo "Creating WSL package in $TMPDIR"

# Copy necessary files
mkdir -p "$TMPDIR/bin"
mkdir -p "$TMPDIR/dist"

# Copy the CLI entry point
cp -r bin/alive.js "$TMPDIR/bin/alive.js"

# Copy the built distribution
cp -r dist "$TMPDIR/dist"

# Copy source for sourcemaps
mkdir -p "$TMPDIR/src"
cp -r src "$TMPDIR/src"

# Copy README files
cp ../README.md "$TMPDIR/ORIGINAL_README.md" || true
cp WSL_README.md "$TMPDIR/README.md" || true

# Create a version based on timestamp
VERSION="$(printf '0.1.%d' "$(date +%y%m%d%H%M)")"

# Modify package.json with the new version
jq --arg version "$VERSION" \
   '.version = $version | .name = "alive-ai-wsl"' \
   package.json > "$TMPDIR/package.json"

# Install native dependencies if available
if [ -f "./scripts/install_native_deps.sh" ]; then
  ./scripts/install_native_deps.sh "$TMPDIR"
fi

# Create a simple installation script for WSL
cat > "$TMPDIR/install.sh" << 'EOF'
#!/bin/bash
# Installation script for Alive AI in WSL

set -euo pipefail

# Check for Node.js
if ! command -v node &> /dev/null; then
  echo "Node.js is required but not installed."
  echo "Please install Node.js 22 or newer:"
  echo "  curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -"
  echo "  sudo apt-get install -y nodejs"
  exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 22 ]; then
  echo "Node.js 22 or newer is required. Found version: $(node -v)"
  echo "Please upgrade Node.js:"
  echo "  curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -"
  echo "  sudo apt-get install -y nodejs"
  exit 1
fi

# Install the package globally
echo "Installing Alive AI..."
npm install -g .

echo "Alive AI has been installed successfully!"
echo "You can now run 'alive' from anywhere in your WSL environment."
EOF

chmod +x "$TMPDIR/install.sh"

# Create a tarball
TARBALL="alive-ai-wsl-$VERSION.tar.gz"
tar -czf "$TARBALL" -C "$TMPDIR" .

echo "Created WSL package: $TARBALL"
echo ""
echo "To install in WSL:"
echo "1. Copy $TARBALL to your WSL environment"
echo "2. Extract with: tar -xzf $TARBALL"
echo "3. Run: ./install.sh"
