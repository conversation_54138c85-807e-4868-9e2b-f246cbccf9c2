/**
 * Code Analysis Command
 *
 * This command analyzes code files and provides insights about code quality,
 * potential issues, and suggestions for improvement.
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import chalk from 'chalk';
import { loadConfig } from '../utils/config';
import { createOpenAIClient } from '../utils/openai-client';

/**
 * Supported programming languages for code analysis
 */
export enum ProgrammingLanguage {
  JavaScript = 'javascript',
  TypeScript = 'typescript',
  Python = 'python',
  Java = 'java',
  CSharp = 'csharp',
  Go = 'go',
  Rust = 'rust',
  Ruby = 'ruby',
  PHP = 'php',
  Swift = 'swift',
  Kotlin = 'kotlin',
  Unknown = 'unknown'
}

/**
 * Code analysis result
 */
export interface CodeAnalysisResult {
  language: ProgrammingLanguage;
  complexity: number;
  issues: Array<{
    type: 'error' | 'warning' | 'info';
    message: string;
    line?: number;
    column?: number;
  }>;
  suggestions: Array<{
    message: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  summary: string;
}

/**
 * Detect programming language from file extension
 *
 * @param filePath Path to the file
 * @returns Detected programming language
 */
function detectLanguage(filePath: string): ProgrammingLanguage {
  const ext = path.extname(filePath).toLowerCase();

  switch (ext) {
    case '.js':
      return ProgrammingLanguage.JavaScript;
    case '.ts':
    case '.tsx':
      return ProgrammingLanguage.TypeScript;
    case '.py':
      return ProgrammingLanguage.Python;
    case '.java':
      return ProgrammingLanguage.Java;
    case '.cs':
      return ProgrammingLanguage.CSharp;
    case '.go':
      return ProgrammingLanguage.Go;
    case '.rs':
      return ProgrammingLanguage.Rust;
    case '.rb':
      return ProgrammingLanguage.Ruby;
    case '.php':
      return ProgrammingLanguage.PHP;
    case '.swift':
      return ProgrammingLanguage.Swift;
    case '.kt':
      return ProgrammingLanguage.Kotlin;
    default:
      return ProgrammingLanguage.Unknown;
  }
}

/**
 * Analyze code using AI
 *
 * @param code Code to analyze
 * @param language Programming language
 * @param model Model to use for analysis
 * @param config App configuration
 * @returns Code analysis result
 */
async function analyzeCode(
  code: string,
  language: ProgrammingLanguage,
  model: string,
  config: any
): Promise<CodeAnalysisResult> {
  try {
    // Create OpenAI client
    const oai = createOpenAIClient(config);

    // Prepare prompt for code analysis
    const response = await oai.chat.completions.create({
      model,
      messages: [
        {
          role: "system",
          content: `You are an expert code analyzer. Analyze the provided ${language} code and provide detailed feedback on:
          1. Code complexity (on a scale of 1-10)
          2. Potential issues (errors, warnings, or informational items)
          3. Suggestions for improvement
          4. A brief summary of the code quality

          Format your response as a JSON object with the following structure:
          {
            "complexity": number,
            "issues": [{"type": "error"|"warning"|"info", "message": string, "line": number?, "column": number?}],
            "suggestions": [{"message": string, "priority": "high"|"medium"|"low"}],
            "summary": string
          }

          Only respond with valid JSON.`
        },
        {
          role: "user",
          content: code
        }
      ],
      response_format: { type: "json_object" }
    });

    // Parse the response
    const analysisText = response.choices[0]?.message.content || '{}';
    const analysis = JSON.parse(analysisText);

    return {
      language,
      complexity: analysis.complexity || 0,
      issues: analysis.issues || [],
      suggestions: analysis.suggestions || [],
      summary: analysis.summary || "No analysis available."
    };
  } catch (error) {
    console.error(`Error analyzing code: ${error}`);
    return {
      language,
      complexity: 0,
      issues: [{
        type: 'error',
        message: `Failed to analyze code: ${error instanceof Error ? error.message : String(error)}`
      }],
      suggestions: [],
      summary: "Analysis failed."
    };
  }
}

/**
 * Analyze a file
 *
 * @param filePath Path to the file
 * @param model Model to use for analysis
 * @param config App configuration
 * @returns Code analysis result
 */
async function analyzeFile(
  filePath: string,
  model: string,
  config: any
): Promise<CodeAnalysisResult> {
  try {
    // Read file content
    const code = await fs.readFile(filePath, 'utf-8');

    // Detect language
    const language = detectLanguage(filePath);

    // Analyze code
    return await analyzeCode(code, language, model, config);
  } catch (error) {
    console.error(`Error analyzing file ${filePath}: ${error}`);
    return {
      language: ProgrammingLanguage.Unknown,
      complexity: 0,
      issues: [{
        type: 'error',
        message: `Failed to analyze file: ${error instanceof Error ? error.message : String(error)}`
      }],
      suggestions: [],
      summary: "Analysis failed."
    };
  }
}

/**
 * Format code analysis result for terminal output
 *
 * @param result Code analysis result
 * @param filePath Path to the analyzed file
 * @returns Formatted string for terminal output
 */
function formatAnalysisResult(result: CodeAnalysisResult, filePath: string): string {
  const lines = [
    chalk.bold(`Analysis for ${path.basename(filePath)} (${result.language}):`),
    '',
    `${chalk.bold('Complexity:')} ${getComplexityColor(result.complexity)(result.complexity.toString())} / 10`,
    '',
    chalk.bold('Issues:')
  ];

  if (result.issues.length === 0) {
    lines.push('  No issues found.');
  } else {
    result.issues.forEach(issue => {
      const issueColor = issue.type === 'error' ? chalk.red :
                         issue.type === 'warning' ? chalk.yellow :
                         chalk.blue;

      const location = issue.line ? ` (line ${issue.line}${issue.column ? `, column ${issue.column}` : ''})` : '';
      lines.push(`  ${issueColor('•')} ${issueColor.bold(issue.type.toUpperCase())}${location}: ${issue.message}`);
    });
  }

  lines.push('', chalk.bold('Suggestions:'));

  if (result.suggestions.length === 0) {
    lines.push('  No suggestions available.');
  } else {
    result.suggestions.forEach(suggestion => {
      const priorityColor = suggestion.priority === 'high' ? chalk.red :
                           suggestion.priority === 'medium' ? chalk.yellow :
                           chalk.green;

      lines.push(`  ${priorityColor('•')} ${suggestion.message} ${priorityColor(`[${suggestion.priority}]`)}`);
    });
  }

  lines.push('', chalk.bold('Summary:'), `  ${result.summary}`);

  return lines.join('\n');
}

/**
 * Get color function based on complexity score
 *
 * @param complexity Complexity score (1-10)
 * @returns Chalk color function
 */
function getComplexityColor(complexity: number) {
  if (complexity <= 3) return chalk.green;
  if (complexity <= 6) return chalk.yellow;
  return chalk.red;
}

/**
 * Analyze command implementation
 */

/**
 * Analyze command implementation
 *
 * @param filePaths Paths to files to analyze
 * @param options Command options
 */
export async function analyzeCommand(filePaths: string[], options: { model?: string } = {}) {
  try {
    // Load config
    const config = await loadConfig();

    // Use specified model or default from config
    const model = options.model || config.model;

    console.log(chalk.bold.blue('Alive AI Code Analysis'));
    console.log(chalk.dim(`Using model: ${model}`));
    console.log('');

    // Analyze each file
    for (const filePath of filePaths) {
      try {
        // Check if file exists
        await fs.access(filePath);

        console.log(chalk.dim(`Analyzing ${filePath}...`));

        // Analyze file
        const result = await analyzeFile(filePath, model, config);

        // Print analysis result
        console.log(formatAnalysisResult(result, filePath));

        // Add separator between files
        if (filePaths.length > 1 && filePath !== filePaths[filePaths.length - 1]) {
          console.log('\n' + '─'.repeat(80) + '\n');
        }
      } catch (error) {
        console.error(chalk.red(`Error analyzing ${filePath}: ${error instanceof Error ? error.message : String(error)}`));
      }
    }
  } catch (error) {
    console.error(chalk.red(`Error: ${error instanceof Error ? error.message : String(error)}`));
    process.exit(1);
  }
}
