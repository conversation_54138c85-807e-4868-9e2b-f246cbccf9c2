/**
 * Code Analysis Utility
 * 
 * This module provides functions for analyzing code and providing insights
 * about code quality, potential issues, and suggestions for improvement.
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { createOpenAIClient } from './openai-client';
import type { AppConfig } from './config';

/**
 * Supported programming languages for code analysis
 */
export enum ProgrammingLanguage {
  JavaScript = 'javascript',
  TypeScript = 'typescript',
  Python = 'python',
  Java = 'java',
  CSharp = 'csharp',
  Go = 'go',
  Rust = 'rust',
  Ruby = 'ruby',
  PHP = 'php',
  Swift = 'swift',
  Kotlin = 'kotlin',
  Unknown = 'unknown'
}

/**
 * Code analysis result
 */
export interface CodeAnalysisResult {
  language: ProgrammingLanguage;
  complexity: number;
  issues: Array<{
    type: 'error' | 'warning' | 'info';
    message: string;
    line?: number;
    column?: number;
  }>;
  suggestions: Array<{
    message: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  summary: string;
}

/**
 * Detect programming language from file extension
 * 
 * @param filePath Path to the file
 * @returns Detected programming language
 */
export function detectLanguage(filePath: string): ProgrammingLanguage {
  const ext = path.extname(filePath).toLowerCase();
  
  switch (ext) {
    case '.js':
      return ProgrammingLanguage.JavaScript;
    case '.ts':
    case '.tsx':
      return ProgrammingLanguage.TypeScript;
    case '.py':
      return ProgrammingLanguage.Python;
    case '.java':
      return ProgrammingLanguage.Java;
    case '.cs':
      return ProgrammingLanguage.CSharp;
    case '.go':
      return ProgrammingLanguage.Go;
    case '.rs':
      return ProgrammingLanguage.Rust;
    case '.rb':
      return ProgrammingLanguage.Ruby;
    case '.php':
      return ProgrammingLanguage.PHP;
    case '.swift':
      return ProgrammingLanguage.Swift;
    case '.kt':
      return ProgrammingLanguage.Kotlin;
    default:
      return ProgrammingLanguage.Unknown;
  }
}

/**
 * Analyze code using AI
 * 
 * @param code Code to analyze
 * @param language Programming language
 * @param model Model to use for analysis
 * @param config App configuration
 * @returns Code analysis result
 */
export async function analyzeCode(
  code: string,
  language: ProgrammingLanguage,
  model: string,
  config: AppConfig
): Promise<CodeAnalysisResult> {
  try {
    // Create OpenAI client
    const oai = createOpenAIClient(config);
    
    // Prepare prompt for code analysis
    const response = await oai.chat.completions.create({
      model,
      messages: [
        {
          role: "system",
          content: `You are an expert code analyzer. Analyze the provided ${language} code and provide detailed feedback on:
          1. Code complexity (on a scale of 1-10)
          2. Potential issues (errors, warnings, or informational items)
          3. Suggestions for improvement
          4. A brief summary of the code quality
          
          Format your response as a JSON object with the following structure:
          {
            "complexity": number,
            "issues": [{"type": "error"|"warning"|"info", "message": string, "line": number?, "column": number?}],
            "suggestions": [{"message": string, "priority": "high"|"medium"|"low"}],
            "summary": string
          }
          
          Only respond with valid JSON.`
        },
        {
          role: "user",
          content: code
        }
      ],
      response_format: { type: "json_object" }
    });
    
    // Parse the response
    const analysisText = response.choices[0]?.message.content || '{}';
    const analysis = JSON.parse(analysisText);
    
    return {
      language,
      complexity: analysis.complexity || 0,
      issues: analysis.issues || [],
      suggestions: analysis.suggestions || [],
      summary: analysis.summary || "No analysis available."
    };
  } catch (error) {
    console.error(`Error analyzing code: ${error}`);
    return {
      language,
      complexity: 0,
      issues: [{
        type: 'error',
        message: `Failed to analyze code: ${error instanceof Error ? error.message : String(error)}`
      }],
      suggestions: [],
      summary: "Analysis failed."
    };
  }
}

/**
 * Analyze a file
 * 
 * @param filePath Path to the file
 * @param model Model to use for analysis
 * @param config App configuration
 * @returns Code analysis result
 */
export async function analyzeFile(
  filePath: string,
  model: string,
  config: AppConfig
): Promise<CodeAnalysisResult> {
  try {
    // Read file content
    const code = await fs.readFile(filePath, 'utf-8');
    
    // Detect language
    const language = detectLanguage(filePath);
    
    // Analyze code
    return await analyzeCode(code, language, model, config);
  } catch (error) {
    console.error(`Error analyzing file ${filePath}: ${error}`);
    return {
      language: ProgrammingLanguage.Unknown,
      complexity: 0,
      issues: [{
        type: 'error',
        message: `Failed to analyze file: ${error instanceof Error ? error.message : String(error)}`
      }],
      suggestions: [],
      summary: "Analysis failed."
    };
  }
}
