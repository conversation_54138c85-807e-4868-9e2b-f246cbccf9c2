#!/usr/bin/env node
/**
 * Web Tool Test Script
 * 
 * This script demonstrates the functionality of the web tool by performing
 * various operations like fetching URLs, scraping webpages, making API requests,
 * and performing searches.
 * 
 * Usage:
 *   node scripts/test-web-tool.js [operation] [args...]
 * 
 * Examples:
 *   node scripts/test-web-tool.js fetch https://example.com
 *   node scripts/test-web-tool.js scrape https://example.com h1
 *   node scripts/test-web-tool.js search duckduckgo "alive ai"
 */

import { fetchUrl, scrapeWebpage, makeApiRequest, performSearch } from '../dist/utils/web-tool.js';
import { loadExistingWebToolKeys } from '../dist/utils/get-web-tool-keys.js';

// ANSI color codes for terminal output
const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

/**
 * Print a colored header
 */
function printHeader(text) {
  console.log(`\n${COLORS.fg.cyan}${COLORS.bright}=== ${text} ===${COLORS.reset}\n`);
}

/**
 * Print a success message
 */
function printSuccess(text) {
  console.log(`${COLORS.fg.green}✓ ${text}${COLORS.reset}`);
}

/**
 * Print an error message
 */
function printError(text) {
  console.log(`${COLORS.fg.red}✗ ${text}${COLORS.reset}`);
}

/**
 * Print the result of a web tool operation
 */
function printResult(result) {
  if (result.success) {
    printSuccess('Operation successful');
    
    if (result.metadata) {
      console.log(`\n${COLORS.fg.yellow}Metadata:${COLORS.reset}`);
      console.log(JSON.stringify(result.metadata, null, 2));
    }
    
    console.log(`\n${COLORS.fg.yellow}Output:${COLORS.reset}`);
    console.log(result.output);
  } else {
    printError(`Operation failed: ${result.error}`);
  }
}

/**
 * Fetch a URL
 */
async function testFetch(url, method = 'GET', body = undefined) {
  printHeader(`Fetching URL: ${url}`);
  
  try {
    const result = await fetchUrl(url, method, body);
    printResult(result);
  } catch (error) {
    printError(`Error: ${error.message}`);
  }
}

/**
 * Scrape a webpage
 */
async function testScrape(url, selector = undefined, extractContents = true) {
  printHeader(`Scraping webpage: ${url}${selector ? ` (selector: ${selector})` : ''}`);
  
  try {
    const result = await scrapeWebpage(url, {
      selector,
      extractContents: extractContents === 'false' ? false : true
    });
    printResult(result);
  } catch (error) {
    printError(`Error: ${error.message}`);
  }
}

/**
 * Make an API request
 */
async function testApi(url, method = 'GET', body = undefined, apiKey = undefined) {
  printHeader(`Making API request: ${method} ${url}`);
  
  try {
    const result = await makeApiRequest(url, method, body, { apiKey });
    printResult(result);
  } catch (error) {
    printError(`Error: ${error.message}`);
  }
}

/**
 * Perform a search
 */
async function testSearch(provider, query, numResults = 5) {
  printHeader(`Searching with ${provider}: "${query}"`);
  
  try {
    // Load API keys
    const webToolKeys = loadExistingWebToolKeys();
    
    // Determine API key based on provider
    let apiKey;
    let cx;
    
    if (provider === 'google') {
      apiKey = webToolKeys.googleApiKey;
      cx = webToolKeys.googleSearchCx;
      
      if (!apiKey) {
        printError('Google search requires an API key. Set it using the manage-web-keys.js script.');
        return;
      }
      
      if (!cx) {
        printError('Google search requires a Custom Search Engine ID. Set it using the manage-web-keys.js script.');
        return;
      }
    } else if (provider === 'bing') {
      apiKey = webToolKeys.bingApiKey;
      
      if (!apiKey) {
        printError('Bing search requires an API key. Set it using the manage-web-keys.js script.');
        return;
      }
    }
    
    const result = await performSearch({
      provider,
      query,
      apiKey,
      cx,
      numResults: parseInt(numResults, 10)
    });
    
    printResult(result);
  } catch (error) {
    printError(`Error: ${error.message}`);
  }
}

/**
 * Print usage information
 */
function printUsage() {
  console.log(`
${COLORS.bright}Web Tool Test Script${COLORS.reset}

This script demonstrates the functionality of the web tool by performing
various operations like fetching URLs, scraping webpages, making API requests,
and performing searches.

${COLORS.fg.yellow}Usage:${COLORS.reset}
  node scripts/test-web-tool.js [operation] [args...]

${COLORS.fg.yellow}Operations:${COLORS.reset}
  fetch <url> [method] [body]
    Fetch content from a URL
    
  scrape <url> [selector] [extractContents]
    Scrape content from a webpage
    
  api <url> [method] [body] [apiKey]
    Make an API request
    
  search <provider> <query> [numResults]
    Perform a search using the specified provider
    Available providers: google, bing, duckduckgo, stackoverflow, github

${COLORS.fg.yellow}Examples:${COLORS.reset}
  node scripts/test-web-tool.js fetch https://example.com
  node scripts/test-web-tool.js scrape https://example.com h1
  node scripts/test-web-tool.js search duckduckgo "alive ai" 3
  `);
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    printUsage();
    return;
  }
  
  const operation = args[0];
  
  switch (operation) {
    case 'fetch':
      if (args.length < 2) {
        printError('Missing URL argument');
        printUsage();
        return;
      }
      await testFetch(args[1], args[2], args[3]);
      break;
      
    case 'scrape':
      if (args.length < 2) {
        printError('Missing URL argument');
        printUsage();
        return;
      }
      await testScrape(args[1], args[2], args[3]);
      break;
      
    case 'api':
      if (args.length < 2) {
        printError('Missing URL argument');
        printUsage();
        return;
      }
      await testApi(args[1], args[2], args[3], args[4]);
      break;
      
    case 'search':
      if (args.length < 3) {
        printError('Missing provider or query argument');
        printUsage();
        return;
      }
      await testSearch(args[1], args[2], args[3]);
      break;
      
    default:
      printError(`Unknown operation: ${operation}`);
      printUsage();
      return;
  }
}

// Run the script
main().catch(error => {
  printError(`Unhandled error: ${error.message}`);
  process.exit(1);
});
