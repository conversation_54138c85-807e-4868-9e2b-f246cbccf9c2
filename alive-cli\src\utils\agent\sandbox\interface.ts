export enum SandboxType {
  NONE = "none",
  MACOS_SEATBELT = "macos.seatbelt",
  LINUX_LANDLOCK = "linux.landlock",
}

/**
 * Input parameters for executing a command
 */
export type ExecInput = {
  /**
   * The command to execute as an array of strings
   */
  cmd: Array<string>;
  
  /**
   * Working directory for the command. If undefined, the current working directory is used.
   */
  workdir: string | undefined;
  
  /**
   * Timeout in milliseconds after which the command will be terminated
   */
  timeoutInMillis: number | undefined;
  
  /**
   * Additional environment variables to set for command execution
   */
  environmentVars?: Record<string, string>;
  
  /**
   * Whether to continue execution even if the command exits with non-zero code.
   * When true, the command's exit code is altered to 0 in the returned result.
   */
  ignoreErrors?: boolean;
  
  /**
   * When true, the command is started in the background and control returns immediately
   * with a success result, without waiting for the command to complete.
   */
  runInBackground?: boolean;
  
  /**
   * Input to provide to the command via stdin
   */
  stdin?: string;
  
  /**
   * Maximum number of output lines to return (0 means no limit)
   */
  maxOutputLines?: number;
  
  /**
   * Specific shell to use for execution (e.g., 'bash', 'powershell', 'zsh')
   */
  shell?: string;
  
  /**
   * Specify output format preference: 'text' (default), 'json', or 'table'.
   * When set to 'json', attempts to parse and pretty-print the output as JSON.
   * When set to 'table', attempts basic formatting of CSV-like data.
   */
  outputFormat?: 'text' | 'json' | 'table';
};

/**
 * Result of executing a command. Caller is responsible for checking `code` to
 * determine whether the command was successful.
 */
export type ExecResult = {
  stdout: string;
  stderr: string;
  exitCode: number;
};

/**
 * Value to use with the `metadata` field of a `ResponseItem` whose type is
 * `function_call_output`.
 */
export type ExecOutputMetadata = {
  exit_code: number;
  duration_seconds: number;
};
