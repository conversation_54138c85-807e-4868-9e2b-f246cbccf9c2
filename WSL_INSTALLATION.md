# Installing Alive AI in Windows 11 WSL

This guide provides instructions for installing and using Alive AI in Windows Subsystem for Linux (WSL2) on Windows 11.

## Prerequisites

1. **Windows 11** with WSL2 installed
2. **Ubuntu** or another Debian-based distribution in WSL
3. **Node.js 22 or newer** installed in your WSL environment

If you haven't set up WSL2 yet, follow [Microsoft's official guide](https://learn.microsoft.com/en-us/windows/wsl/install).

## Installation Steps

### 1. Install Node.js in WSL

Open your WSL terminal and run the following commands to install Node.js 22:

```bash
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs
```

Verify the installation:

```bash
node --version
```

Make sure it shows v22.x.x or newer.

### 2. Install Alive AI

#### Option 1: Using the prepared package

1. Copy the `alive-ai-wsl-x.x.x.tar.gz` file to your WSL environment
2. Extract the package:

```bash
tar -xzf alive-ai-wsl-x.x.x.tar.gz
```

3. Run the installation script:

```bash
./install.sh
```

#### Option 2: Building from source

If you prefer to build from source:

1. Clone the repository in your WSL environment:

```bash
git clone https://github.com/alive-ai/alive-ai.git
cd alive-ai
```

2. Install dependencies and build:

```bash
npm install -g pnpm@10.8.1
pnpm install
cd alive-cli
pnpm run build
```

3. Create the WSL package:

```bash
bash scripts/create_wsl_package.sh
```

4. Follow the instructions displayed after creating the package.

## Usage

After installation, you can use Alive AI from anywhere in your WSL environment:

```bash
alive "Write and run a python program that prints ASCII art"
```

### Common Commands

- Get help: `alive --help`
- Use a specific model: `alive --model <model-name> "your prompt"`
- Include an image: `alive --image /path/to/image.png "describe this image"`
- Browse previous sessions: `alive --history`

### Configuration

Alive AI stores its configuration in `~/.alive-ai/config.toml`. You can edit this file to customize your experience:

```toml
# Example configuration
model = "alive-mini-latest"
model_provider = "openai"

[sandbox_permissions]
disk_write_cwd = true
disk_write_tmp = true
network = true
```

## Troubleshooting

### Permission Issues

If you encounter permission issues, make sure the scripts have execute permissions:

```bash
chmod +x install.sh
```

### Network Connectivity

If you experience network issues, verify that your WSL environment has internet connectivity:

```bash
ping api.openai.com
```

### API Key Configuration

Alive AI requires an OpenAI API key. Set it in your environment:

```bash
export OPENAI_API_KEY="your-api-key"
```

Or add it to your `~/.bashrc` or `~/.zshrc` file for persistence.

## Additional Resources

- [Alive AI Documentation](https://github.com/alive-ai/alive-ai)
- [WSL Documentation](https://learn.microsoft.com/en-us/windows/wsl/)
- [Node.js Documentation](https://nodejs.org/docs/latest-v22.x/api/)

## Support

If you encounter any issues specific to the WSL installation, please file an issue on the GitHub repository.
