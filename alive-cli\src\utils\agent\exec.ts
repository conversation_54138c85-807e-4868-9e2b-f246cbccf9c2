import type { AppConfig } from "../config.js";
import type { ExecInput, ExecResult } from "./sandbox/interface.js";
import type { SpawnOptions } from "child_process";
import type { ParseEntry } from "shell-quote";

import { process_patch } from "./apply-patch.js";
import { SandboxType } from "./sandbox/interface.js";
import { execWithLandlock } from "./sandbox/landlock.js";
import { execWithSeatbelt } from "./sandbox/macos-seatbelt.js";
import { exec as rawExec } from "./sandbox/raw-exec.js";
import { formatCommandForDisplay } from "../../format-command.js";
import { log } from "../logger/log.js";
import fs from "fs";
import os from "os";
import path from "path";
import { parse } from "shell-quote";
import { resolvePathAgainstWorkdir } from "src/approvals.js";
import { PATCH_SUFFIX } from "src/parse-apply-patch.js";

const DEFAULT_TIMEOUT_MS = 10_000; // 10 seconds

function requiresShell(cmd: Array<string>): boolean {
  // If the command is a single string that contains shell operators,
  // it needs to be run with shell: true
  if (cmd.length === 1 && cmd[0] !== undefined) {
    const tokens = parse(cmd[0]) as Array<ParseEntry>;
    return tokens.some((token) => typeof token === "object" && "op" in token);
  }

  // If the command is split into multiple arguments, we don't need shell: true
  // even if one of the arguments is a shell operator like '|'
  return false;
}

/**
 * This function should never return a rejected promise: errors should be
 * mapped to a non-zero exit code and the error message should be in stderr.
 */
export function exec(
  {
    cmd,
    workdir,
    timeoutInMillis,
    additionalWritableRoots,
    environmentVars,
    ignoreErrors,
    runInBackground,
    stdin,
    maxOutputLines,
    shell,
    outputFormat,
  }: ExecInput & { 
    additionalWritableRoots: ReadonlyArray<string>,
    environmentVars?: Record<string, string>,
    ignoreErrors?: boolean,
    runInBackground?: boolean,
    stdin?: string,
    maxOutputLines?: number,
    shell?: string,
    outputFormat?: 'text' | 'json' | 'table'
  },
  sandbox: SandboxType,
  config: AppConfig,
  abortSignal?: AbortSignal,
): Promise<ExecResult> {
  const opts: SpawnOptions = {
    timeout: timeoutInMillis || DEFAULT_TIMEOUT_MS,
    ...(shell ? { shell } : requiresShell(cmd) ? { shell: true } : {}),
    ...(workdir ? { cwd: workdir } : {}),
    ...(environmentVars ? { env: { ...process.env, ...environmentVars } } : {}),
    ...(stdin ? { stdio: ['pipe', 'pipe', 'pipe'] } : {}),
  };

  log(`EXEC with options: ${JSON.stringify({
    sandbox,
    workdir: opts.cwd,
    timeout: opts.timeout,
    shell: opts.shell,
    envVarsSet: environmentVars ? Object.keys(environmentVars) : [],
    ignoreErrors,
    runInBackground,
    hasStdin: Boolean(stdin),
    maxOutputLines,
    outputFormat,
  })}`);

  const execPromise = (() => {
    switch (sandbox) {
      case SandboxType.NONE: {
        // SandboxType.NONE uses the raw exec implementation.
        return rawExec(cmd, opts, config, abortSignal, stdin);
      }
      case SandboxType.MACOS_SEATBELT: {
        // Merge default writable roots with any user-specified ones.
        const writableRoots = [
          process.cwd(),
          os.tmpdir(),
          ...additionalWritableRoots,
        ];
        return execWithSeatbelt(cmd, opts, writableRoots, config, abortSignal, stdin);
      }
      case SandboxType.LINUX_LANDLOCK: {
        return execWithLandlock(
          cmd,
          opts,
          additionalWritableRoots,
          config,
          abortSignal,
          stdin
        );
      }
    }
  })();

  // If running in background, return a success result immediately
  if (runInBackground) {
    return Promise.resolve({
      stdout: `Command started in background: ${formatCommandForDisplay(cmd)}`,
      stderr: "",
      exitCode: 0,
    });
  }

  // Process the result based on the new parameters
  return execPromise.then(result => {
    // Apply max output lines limit if specified
    if (maxOutputLines && maxOutputLines > 0) {
      const stdout = result.stdout.split('\n');
      const stderr = result.stderr.split('\n');
      
      if (stdout.length > maxOutputLines) {
        const truncated = stdout.slice(0, maxOutputLines);
        truncated.push(`[Output truncated, ${stdout.length - maxOutputLines} more lines]`);
        result.stdout = truncated.join('\n');
      }
      
      if (stderr.length > maxOutputLines) {
        const truncated = stderr.slice(0, maxOutputLines);
        truncated.push(`[Error output truncated, ${stderr.length - maxOutputLines} more lines]`);
        result.stderr = truncated.join('\n');
      }
    }

    // Format the output based on the specified format
    if (outputFormat && result.exitCode === 0) {
      try {
        if (outputFormat === 'json' && result.stdout.trim()) {
          // Try to parse and pretty-print JSON
          const jsonData = JSON.parse(result.stdout);
          result.stdout = JSON.stringify(jsonData, null, 2);
        } else if (outputFormat === 'table' && result.stdout.trim()) {
          // Simple table formatting for CSV-like data
          const lines = result.stdout.trim().split('\n');
          if (lines.length > 1) {
            // Format as table if possible
            const formattedLines = lines.map(line => {
              if (line.includes(',')) {
                return line.split(',').join('\t');
              }
              return line;
            });
            result.stdout = formattedLines.join('\n');
          }
        }
      } catch (error) {
        // If formatting fails, keep original output but add a note
        result.stderr += (result.stderr ? '\n' : '') + 
          `[Note: Could not format output as ${outputFormat}: ${error}]`;
      }
    }

    // If ignoreErrors is set, we'll modify the result to always have exitCode 0
    if (ignoreErrors && result.exitCode !== 0) {
      log(`Command exited with code ${result.exitCode}, but ignoring due to ignoreErrors=true`);
      return {
        ...result,
        stdout: result.stdout + (result.stdout ? '\n' : '') + 
          `[Note: Command actually exited with code ${result.exitCode}, but ignoreErrors=true was set]`,
        exitCode: 0
      };
    }
    
    return result;
  });
}

export function execApplyPatch(
  patchText: string,
  workdir: string | undefined = undefined,
): ExecResult {
  // This find/replace is required from some models like 4.1 where the patch
  // text is wrapped in quotes that breaks the apply_patch command.
  let applyPatchInput = patchText
    .replace(/('|")?<<('|")EOF('|")/, "")
    .replace(/\*\*\* End Patch\nEOF('|")?/, "*** End Patch")
    .trim();

  if (!applyPatchInput.endsWith(PATCH_SUFFIX)) {
    applyPatchInput += "\n" + PATCH_SUFFIX;
  }

  log(`Applying patch: \`\`\`${applyPatchInput}\`\`\`\n\n`);

  try {
    const result = process_patch(
      applyPatchInput,
      (p) => fs.readFileSync(resolvePathAgainstWorkdir(p, workdir), "utf8"),
      (p, c) => {
        const resolvedPath = resolvePathAgainstWorkdir(p, workdir);

        // Ensure the parent directory exists before writing the file. This
        // mirrors the behaviour of the standalone apply_patch CLI (see
        // write_file() in apply-patch.ts) and prevents errors when adding a
        // new file in a not‑yet‑created sub‑directory.
        const dir = path.dirname(resolvedPath);
        if (dir !== ".") {
          fs.mkdirSync(dir, { recursive: true });
        }

        fs.writeFileSync(resolvedPath, c, "utf8");
      },
      (p) => fs.unlinkSync(resolvePathAgainstWorkdir(p, workdir)),
    );
    return {
      stdout: result,
      stderr: "",
      exitCode: 0,
    };
  } catch (error: unknown) {
    // @ts-expect-error error might not be an object or have a message property.
    const stderr = String(error.message ?? error);
    return {
      stdout: "",
      stderr: stderr,
      exitCode: 1,
    };
  }
}

export function getBaseCmd(cmd: Array<string>): string {
  const formattedCommand = formatCommandForDisplay(cmd);
  return formattedCommand.split(" ")[0] || cmd[0] || "<unknown>";
}
