import type { OverlayModeType } from "./terminal-chat.js";
import type { TerminalHeaderProps } from "./terminal-header.js";
import type { ResponseItem, BatchEntry, GroupedResponseItem } from "../../types/openai";
import type { FileOpenerScheme } from "src/utils/config.js";

import TerminalChatResponseItem from "./terminal-chat-response-item.js";
import TerminalHeader from "./terminal-header.js";
import { ThemeContext } from "./terminal-chat.js";
import { useTerminalSize } from "../../hooks/use-terminal-size.js";
import { THEME_STYLES } from "../theme-overlay.js";
import { Box, Static } from "ink";
import React, { useMemo, useContext } from "react";

type TerminalMessageHistoryProps = {
  batch: Array<BatchEntry>;
  groupCounts: Record<string, number>;
  items: Array<ResponseItem>;
  userMsgCount: number;
  confirmationPrompt: React.ReactNode;
  loading: boolean;
  thinkingSeconds: number;
  headerProps: TerminalHeaderProps;
  fullStdout: boolean;
  setOverlayMode: React.Dispatch<React.SetStateAction<OverlayModeType>>;
  fileOpener: FileOpenerScheme | undefined;
};

const TerminalMessageHistory: React.FC<TerminalMessageHistoryProps> = ({
  batch,
  headerProps,
  // `loading` and `thinkingSeconds` handled by input component now.
  loading: _loading,
  thinkingSeconds: _thinkingSeconds,
  fullStdout,
  setOverlayMode,
  fileOpener,
}) => {
  const { columns } = useTerminalSize();
  const messageWidth = Math.min(columns - 2, 80); // Ensure message doesn't exceed terminal width
  
  // Get theme from context
  const { theme } = useContext(ThemeContext);
  const themeStyles = THEME_STYLES[theme as keyof typeof THEME_STYLES] || THEME_STYLES.default;
  
  // Flatten batch entries to response items.
  const messages = useMemo(() => batch.map(({ item }) => item!), [batch]);

  return (
    <Box flexDirection="column">
      {/* The dedicated thinking indicator in the input area now displays the
          elapsed time, so we no longer render a separate counter here. */}
      <Static items={["header", ...messages]}>
        {(item, index) => {
          if (item === "header") {
            return <TerminalHeader key="header" {...headerProps} themeStyles={themeStyles} />;
          }

          // After the guard above, item is a ResponseItem
          const message = item as ResponseItem;
          // Suppress empty reasoning updates (i.e. items with an empty summary).
          const msg = message as unknown as { summary?: Array<unknown> };
          if (msg.summary?.length === 0) {
            return null;
          }
          
          const isUser = message.type === "message" && message.role === "user";
          const isAssistant = message.type === "message" && message.role === "assistant";
          const isSystem = message.type === "message" && message.role === "system";
          
          // Get correct border colors based on theme and message type
          const getBorderColor = () => {
            if (isUser) return themeStyles.border || "blueBright";
            if (isAssistant) return "magentaBright";
            return undefined;
          };
          
          return (
            <Box
              key={`${message.id}-${index}`}
              flexDirection="column"
              borderStyle={isUser || isAssistant ? "round" : undefined}
              borderColor={getBorderColor()}
              width={isUser || isAssistant ? messageWidth : undefined}
              marginLeft={!isUser && !isAssistant ? 4 : 0}
              marginY={1}
              paddingX={isUser || isAssistant ? 1 : 0}
            >
              <TerminalChatResponseItem
                item={message}
                fullStdout={fullStdout}
                setOverlayMode={setOverlayMode}
                fileOpener={fileOpener}
                themeStyles={themeStyles}
              />
            </Box>
          );
        }}
      </Static>
    </Box>
  );
};

export default React.memo(TerminalMessageHistory);
