import SelectInput from "../components/select-input/select-input.js";
import Spinner from "../components/vendor/ink-spinner.js";
import TextInput from "../components/vendor/ink-text-input.js";
import { Box, Text } from "ink";
import React, { useState } from "react";

export type Choice = { type: "signin" } | { type: "apikey"; key: string };

export type WebToolConfig = {
  googleApiKey?: string;
  bingApiKey?: string;
  googleSearchCx?: string;
  defaultSearchProvider?: 'google' | 'bing' | 'duckduckgo';
};

export function ApiKeyPrompt({
  onDone,
}: {
  onDone: (choice: Choice) => void;
}): JSX.Element {
  const [step, setStep] = useState<"select" | "paste">("select");
  const [apiKey, setApiKey] = useState("");

  if (step === "select") {
    return (
      <Box flexDirection="column" gap={1}>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text bold color="blueBright">
                Welcome to Alive AI
              </Text>
            </Box>
            <Text>
              Sign in with ChatGPT to generate an API key or paste one you already
              have.
            </Text>
            <Box marginTop={1}>
              <Text dimColor>
                [use ↑/↓ arrows to navigate, enter to select]
              </Text>
            </Box>
          </Box>
        </Box>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <SelectInput
            items={[
              {
                label: "Sign in with ChatGPT",
                value: "signin"
              },
              {
                label: "Paste an API key (or set as OPENAI_API_KEY)",
                value: "paste",
              },
            ]}
            onSelect={(item: { value: string }) => {
              if (item.value === "signin") {
                onDone({ type: "signin" });
              } else {
                setStep("paste");
              }
            }}
          />
        </Box>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" gap={1}>
      <Box
        borderStyle="round"
        borderColor="gray"
        paddingX={2}
        paddingY={1}
        width={64}
      >
        <Box flexDirection="column">
          <Box marginBottom={1}>
            <Text bold color="blueBright">
              API Key Required
            </Text>
          </Box>
          <Text>
            Paste your OpenAI API key and press <Text bold>Enter</Text>:
          </Text>
          <Box marginTop={1}>
            <Text dimColor>
              Your key will be masked for security
            </Text>
          </Box>
        </Box>
      </Box>
      <Box
        borderStyle="round"
        borderColor="gray"
        paddingX={2}
        paddingY={1}
        width={64}
      >
        <TextInput
          value={apiKey}
          onChange={setApiKey}
          onSubmit={(value: string) => {
            if (value.trim() !== "") {
              onDone({ type: "apikey", key: value.trim() });
            }
          }}
          placeholder="sk-..."
          mask="*"
        />
      </Box>
      <Box paddingX={2}>
        <Text dimColor>
          Press <Text bold>Esc</Text> to go back to selection
        </Text>
      </Box>
    </Box>
  );
}

export function WaitingForAuth(): JSX.Element {
  return (
    <Box flexDirection="column" gap={1} marginY={1}>
      <Box
        borderStyle="round"
        borderColor="gray"
        paddingX={2}
        paddingY={1}
        width={64}
      >
        <Box flexDirection="row" alignItems="center">
          <Spinner type="ball" />
          <Text>
            {" "}
            Authenticating with OpenAI...
          </Text>
        </Box>
      </Box>
      <Box paddingX={2}>
        <Text dimColor>
          A browser window should open automatically.
          Press <Text bold>Ctrl+C</Text> to cancel.
        </Text>
      </Box>
    </Box>
  );
}

// New component for web tool API key configuration
export function WebToolConfigPrompt({
  onDone,
  onSkip,
}: {
  onDone: (config: WebToolConfig) => void;
  onSkip: () => void;
}): JSX.Element {
  const [step, setStep] = useState<"select" | "google" | "bing" | "cx" | "provider">("select");
  const [googleApiKey, setGoogleApiKey] = useState("");
  const [bingApiKey, setBingApiKey] = useState("");
  const [googleSearchCx, setGoogleSearchCx] = useState("");
  const [searchProvider, setSearchProvider] = useState<'google' | 'bing' | 'duckduckgo'>('duckduckgo');
  
  // Step 1: Choose whether to configure web tool
  if (step === "select") {
    return (
      <Box flexDirection="column" gap={1}>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text bold color="blueBright">
                Web Tool Configuration
              </Text>
            </Box>
            <Text>
              Alive AI can fetch data from the internet when enabled with --enable-web-tool flag.
              Would you like to configure API keys for web search providers now?
            </Text>
            <Box marginTop={1}>
              <Text dimColor>
                You can always configure these later via environment variables or the config file.
              </Text>
            </Box>
          </Box>
        </Box>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <SelectInput
            items={[
              {
                label: "Yes, configure web tool API keys now",
                value: "configure"
              },
              {
                label: "Skip for now",
                value: "skip",
              },
            ]}
            onSelect={(item: { value: string }) => {
              if (item.value === "configure") {
                setStep("google");
              } else {
                onSkip();
              }
            }}
          />
        </Box>
      </Box>
    );
  }
  
  // Step 2: Configure Google API Key
  if (step === "google") {
    return (
      <Box flexDirection="column" gap={1}>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text bold color="blueBright">
                Google API Key (Optional)
              </Text>
            </Box>
            <Text>
              Enter your Google API key for web search or press <Text bold>Enter</Text> to skip:
            </Text>
            <Box marginTop={1}>
              <Text dimColor>
                Your key will be masked for security
              </Text>
            </Box>
          </Box>
        </Box>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <TextInput
            value={googleApiKey}
            onChange={setGoogleApiKey}
            onSubmit={(value: string) => {
              setGoogleApiKey(value.trim());
              setStep("cx");
            }}
            placeholder="Google API Key (press Enter to skip)"
            mask="*"
          />
        </Box>
        <Box paddingX={2}>
          <Text dimColor>
            Press <Text bold>Esc</Text> to go back
          </Text>
        </Box>
      </Box>
    );
  }
  
  // Step 3: Configure Google Custom Search Engine ID (only if Google API Key was provided)
  if (step === "cx") {
    return (
      <Box flexDirection="column" gap={1}>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text bold color="blueBright">
                Google Custom Search Engine ID {googleApiKey ? "(Required)" : "(Optional)"}
              </Text>
            </Box>
            <Text>
              {googleApiKey 
                ? "Enter your Google Custom Search Engine ID:" 
                : "Enter your Google Custom Search Engine ID or press Enter to skip:"}
            </Text>
            <Box marginTop={1}>
              <Text dimColor>
                Required for Google Search API to work
              </Text>
            </Box>
          </Box>
        </Box>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <TextInput
            value={googleSearchCx}
            onChange={setGoogleSearchCx}
            onSubmit={(value: string) => {
              setGoogleSearchCx(value.trim());
              setStep("bing");
            }}
            placeholder="Google CX ID (e.g., 012345678901234567890:abcdef)"
          />
        </Box>
        <Box paddingX={2}>
          <Text dimColor>
            Press <Text bold>Esc</Text> to go back
          </Text>
        </Box>
      </Box>
    );
  }
  
  // Step 4: Configure Bing API Key
  if (step === "bing") {
    return (
      <Box flexDirection="column" gap={1}>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text bold color="blueBright">
                Bing API Key (Optional)
              </Text>
            </Box>
            <Text>
              Enter your Bing API key for web search or press <Text bold>Enter</Text> to skip:
            </Text>
            <Box marginTop={1}>
              <Text dimColor>
                Your key will be masked for security
              </Text>
            </Box>
          </Box>
        </Box>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <TextInput
            value={bingApiKey}
            onChange={setBingApiKey}
            onSubmit={(value: string) => {
              setBingApiKey(value.trim());
              setStep("provider");
            }}
            placeholder="Bing API Key (press Enter to skip)"
            mask="*"
          />
        </Box>
        <Box paddingX={2}>
          <Text dimColor>
            Press <Text bold>Esc</Text> to go back
          </Text>
        </Box>
      </Box>
    );
  }
  
  // Step 5: Select default search provider
  if (step === "provider") {
    return (
      <Box flexDirection="column" gap={1}>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text bold color="blueBright">
                Default Search Provider
              </Text>
            </Box>
            <Text>
              Select the default search provider to use:
            </Text>
            <Box marginTop={1}>
              <Text dimColor>
                DuckDuckGo requires no API key but may have rate limits
              </Text>
            </Box>
          </Box>
        </Box>
        <Box
          borderStyle="round"
          borderColor="gray"
          paddingX={2}
          paddingY={1}
          width={64}
        >
          <SelectInput
            items={[
              ...(googleApiKey && googleSearchCx ? [{ label: "Google Search", value: "google" }] : []),
              ...(bingApiKey ? [{ label: "Bing Search", value: "bing" }] : []),
              { label: "DuckDuckGo (no API key required)", value: "duckduckgo" },
            ]}
            onSelect={(item: { value: string }) => {
              setSearchProvider(item.value as 'google' | 'bing' | 'duckduckgo');
              onDone({
                googleApiKey,
                bingApiKey,
                googleSearchCx,
                defaultSearchProvider: item.value as 'google' | 'bing' | 'duckduckgo'
              });
            }}
          />
        </Box>
        <Box paddingX={2}>
          <Text dimColor>
            Press <Text bold>Esc</Text> to go back
          </Text>
        </Box>
      </Box>
    );
  }

  // Should never reach here
  return <Box />;
}
