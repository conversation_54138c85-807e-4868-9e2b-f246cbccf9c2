#!/bin/bash
set -e

# Script to build both alive-rs and alive-cli components
echo "Building Alive AI components..."

# Build the Rust component (alive-rs)
echo "Building alive-rs (Rust component)..."
cd "$(dirname "$0")/../alive-rs"
cargo build --release

# Build the CLI component (alive-cli)
echo "Building alive-cli (Node.js component)..."
cd "../alive-cli"
npm ci
npm run build

echo "Build completed successfully!" 