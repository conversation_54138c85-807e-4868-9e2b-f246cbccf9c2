import axios, { AxiosRequestConfig, ResponseType } from 'axios';
import * as cheerio from 'cheerio';
import { AbortController } from 'node-abort-controller';
import { createHash } from 'crypto';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';

// Cache configuration
const CACHE_ENABLED = true;
const CACHE_TTL = 3600 * 1000; // 1 hour in milliseconds
const CACHE_DIR = join(homedir(), '.alive-ai', 'cache', 'web-tool');

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute window
const RATE_LIMITS: Record<string, number> = {
  'default': 60, // 60 requests per minute by default
  'google': 10,  // 10 requests per minute for Google
  'bing': 10,    // 10 requests per minute for Bing
  'duckduckgo': 20, // 20 requests per minute for DuckDuckGo
  'microsoft': 10, // 10 requests per minute for Microsoft
  'yahoo': 10,    // 10 requests per minute for Yahoo
  'stackoverflow': 20, // 20 requests per minute for StackOverflow
  'github': 20    // 20 requests per minute for GitHub
};

// Request tracking for rate limiting
const requestCounts: Record<string, { count: number, resetTime: number }> = {
  'default': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'google': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'bing': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'duckduckgo': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'microsoft': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'yahoo': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'stackoverflow': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW },
  'github': { count: 0, resetTime: Date.now() + RATE_LIMIT_WINDOW }
};

// Types for function arguments
type FetchOptions = {
  timeout?: number;
  headers?: Record<string, string>;
  responseType?: 'json' | 'text' | 'arraybuffer' | 'blob';
  followRedirects?: boolean;
  maxRedirects?: number;
  cacheResults?: boolean; // Whether to cache results
  cacheTtl?: number; // Cache TTL in milliseconds
};

type ScrapeOptions = {
  selector?: string;
  extractContents?: boolean;
  timeout?: number;
  headers?: Record<string, string>;
  followRedirects?: boolean;
  maxRedirects?: number;
  cacheResults?: boolean;
  cacheTtl?: number;
};

type ApiOptions = {
  apiKey?: string;
  endpoint?: string;
  params?: Record<string, string>;
  timeout?: number;
  headers?: Record<string, string>;
  responseType?: 'json' | 'text' | 'arraybuffer' | 'blob';
  followRedirects?: boolean;
  maxRedirects?: number;
  cacheResults?: boolean;
  cacheTtl?: number;
};

type SearchOptions = {
  provider: 'google' | 'bing' | 'duckduckgo' | 'stackoverflow' | 'github' | 'microsoft' | 'yahoo';
  query: string;
  apiKey?: string;
  numResults?: number;
  cx?: string; // Google Custom Search Engine ID
  cacheResults?: boolean;
  cacheTtl?: number;
  filters?: SearchFilters;
};

type SearchFilters = {
  timeRange?: 'day' | 'week' | 'month' | 'year' | 'all';
  siteRestriction?: string; // Restrict search to specific site (e.g., "site:example.com")
  fileType?: string; // Restrict to specific file types (e.g., "pdf", "doc")
  language?: string; // Language code (e.g., "en", "es")
};

type WebToolResponse = {
  output: string;
  error?: string;
  success: boolean;
  metadata?: {
    source?: string;
    timestamp?: number;
    cached?: boolean;
    requestId?: string;
  };
};

/**
 * Utility function to generate a cache key from request parameters
 */
function generateCacheKey(url: string, method: string, body?: string, options?: any): string {
  const data = JSON.stringify({
    url,
    method,
    body,
    options
  });
  return createHash('md5').update(data).digest('hex');
}

/**
 * Utility function to check if a request is cached and return the cached response
 */
function getCachedResponse(cacheKey: string, ttl: number = CACHE_TTL): WebToolResponse | null {
  try {
    // Ensure cache directory exists
    if (!existsSync(CACHE_DIR)) {
      mkdirSync(CACHE_DIR, { recursive: true });
      return null;
    }

    const cacheFile = join(CACHE_DIR, `${cacheKey}.json`);
    if (!existsSync(cacheFile)) {
      return null;
    }

    const cacheData = JSON.parse(readFileSync(cacheFile, 'utf8'));
    const now = Date.now();

    // Check if cache is still valid
    if (cacheData.timestamp + ttl < now) {
      return null;
    }

    return {
      ...cacheData.response,
      metadata: {
        ...cacheData.response.metadata,
        cached: true,
        timestamp: cacheData.timestamp
      }
    };
  } catch (error) {
    // If there's any error reading the cache, just return null
    return null;
  }
}

/**
 * Utility function to save a response to cache
 */
function cacheResponse(cacheKey: string, response: WebToolResponse): void {
  try {
    // Ensure cache directory exists
    if (!existsSync(CACHE_DIR)) {
      mkdirSync(CACHE_DIR, { recursive: true });
    }

    const cacheData = {
      timestamp: Date.now(),
      response
    };

    const cacheFile = join(CACHE_DIR, `${cacheKey}.json`);
    writeFileSync(cacheFile, JSON.stringify(cacheData), 'utf8');
  } catch (error) {
    // Silently fail if caching fails
    console.error('Failed to cache response:', error);
  }
}

/**
 * Utility function to check and enforce rate limits
 * Returns true if the request can proceed, false if it's rate limited
 */
function checkRateLimit(provider: string = 'default'): boolean {
  const now = Date.now();
  const limit = RATE_LIMITS[provider] || RATE_LIMITS.default;

  // Reset counter if the window has passed
  if (now > requestCounts[provider]?.resetTime || !requestCounts[provider]) {
    requestCounts[provider] = {
      count: 0,
      resetTime: now + RATE_LIMIT_WINDOW
    };
  }

  // Check if we're over the limit
  if (requestCounts[provider].count >= limit) {
    return false;
  }

  // Increment the counter
  requestCounts[provider].count++;
  return true;
}

/**
 * Fetches content from a URL with specified options
 */
export async function fetchUrl(
  url: string,
  method: string = 'GET',
  body?: string,
  options: FetchOptions = {}
): Promise<WebToolResponse> {
  if (!url) {
    return {
      output: '',
      error: 'URL is required for fetch operation',
      success: false,
    };
  }

  // Generate a unique request ID
  const requestId = generateCacheKey(url, method, body, options).substring(0, 8);

  // Check rate limits
  if (!checkRateLimit('default')) {
    return {
      output: '',
      error: 'Rate limit exceeded. Please try again later.',
      success: false,
      metadata: {
        requestId
      }
    };
  }

  // Check cache if enabled
  if (options.cacheResults !== false) {
    const cacheKey = generateCacheKey(url, method, body, options);
    const cachedResponse = getCachedResponse(cacheKey, options.cacheTtl);
    if (cachedResponse) {
      return cachedResponse;
    }
  }

  const controller = new AbortController();
  const timeoutId = options.timeout
    ? setTimeout(() => controller.abort(), options.timeout)
    : null;

  try {
    const axiosConfig: AxiosRequestConfig = {
      method,
      url,
      data: body,
      headers: options.headers || {},
      signal: controller.signal,
      responseType: (options.responseType || 'text') as ResponseType,
      maxRedirects: options.maxRedirects !== undefined ? options.maxRedirects : 5,
      validateStatus: () => true, // Don't throw on any status code
    };

    const response = await axios(axiosConfig);

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    let responseOutput = '';

    // Format response based on content type
    if (options.responseType === 'json' ||
        (response.headers['content-type'] && response.headers['content-type'].includes('application/json'))) {
      // If response is JSON or responseType is json, return formatted JSON
      responseOutput = JSON.stringify(response.data, null, 2);
    } else if (typeof response.data === 'object') {
      // If response data is an object but not explicitly JSON, stringify it
      responseOutput = JSON.stringify(response.data, null, 2);
    } else {
      // Otherwise return as text
      responseOutput = String(response.data);
    }

    // Truncate large responses
    if (responseOutput.length > 100000) {
      responseOutput = responseOutput.substring(0, 100000) + '... [truncated - response too large]';
    }

    const webResponse: WebToolResponse = {
      output: responseOutput,
      success: response.status >= 200 && response.status < 300,
      metadata: {
        source: url,
        timestamp: Date.now(),
        requestId
      }
    };

    // Cache the response if caching is enabled
    if (options.cacheResults !== false) {
      const cacheKey = generateCacheKey(url, method, body, options);
      cacheResponse(cacheKey, webResponse);
    }

    return webResponse;
  } catch (error) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (axios.isAxiosError(error)) {
      return {
        output: '',
        error: `Fetch error: ${error.message} [status: ${error.response?.status || 'unknown'}]`,
        success: false,
        metadata: {
          requestId
        }
      };
    }

    return {
      output: '',
      error: `Fetch error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
      metadata: {
        requestId
      }
    };
  }
}

/**
 * Scrapes content from a webpage with optional selector filtering
 */
export async function scrapeWebpage(
  url: string,
  options: ScrapeOptions = {}
): Promise<WebToolResponse> {
  if (!url) {
    return {
      output: '',
      error: 'URL is required for scrape operation',
      success: false,
    };
  }

  // Generate a unique request ID
  const requestId = generateCacheKey(url, 'GET', undefined, options).substring(0, 8);

  // Check rate limits
  if (!checkRateLimit('default')) {
    return {
      output: '',
      error: 'Rate limit exceeded. Please try again later.',
      success: false,
      metadata: {
        requestId
      }
    };
  }

  // Check cache if enabled
  if (options.cacheResults !== false) {
    const cacheKey = generateCacheKey(url, 'GET', undefined, options);
    const cachedResponse = getCachedResponse(cacheKey, options.cacheTtl);
    if (cachedResponse) {
      return cachedResponse;
    }
  }

  try {
    // Fetch the HTML content
    const fetchResult = await fetchUrl(url, 'GET', undefined, {
      timeout: options.timeout,
      headers: options.headers,
      responseType: 'text',
      followRedirects: options.followRedirects,
      maxRedirects: options.maxRedirects,
      // Disable caching for the fetch operation since we'll cache the final result
      cacheResults: false
    });

    if (!fetchResult.success) {
      return {
        ...fetchResult,
        metadata: {
          ...fetchResult.metadata,
          requestId
        }
      };
    }

    const html = fetchResult.output;
    const $ = cheerio.load(html);

    let content: string;

    if (options.selector) {
      // Extract content from specified selector
      const selectedElements = $(options.selector);

      if (selectedElements.length === 0) {
        return {
          output: '',
          error: `No elements found matching selector: ${options.selector}`,
          success: false,
          metadata: {
            source: url,
            requestId
          }
        };
      }

      if (options.extractContents) {
        // Extract text content only
        content = selectedElements.text().trim();
      } else {
        // Extract HTML content
        content = selectedElements.html() || '';
      }
    } else {
      // Process the entire document
      if (options.extractContents) {
        // Get text content from the body, removing scripts and styles
        $('script, style').remove();
        content = $('body').text().trim().replace(/\s+/g, ' ');
      } else {
        // Get the entire HTML
        content = $.html();
      }
    }

    // Truncate large responses
    if (content.length > 100000) {
      content = content.substring(0, 100000) + '... [truncated - response too large]';
    }

    const webResponse: WebToolResponse = {
      output: content,
      success: true,
      metadata: {
        source: url,
        timestamp: Date.now(),
        requestId
      }
    };

    // Cache the response if caching is enabled
    if (options.cacheResults !== false) {
      const cacheKey = generateCacheKey(url, 'GET', undefined, options);
      cacheResponse(cacheKey, webResponse);
    }

    return webResponse;
  } catch (error) {
    return {
      output: '',
      error: `Scrape error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
      metadata: {
        requestId
      }
    };
  }
}

/**
 * Makes an API request with appropriate authentication and parameters
 */
export async function makeApiRequest(
  url: string,
  method: string = 'GET',
  body?: string,
  options: ApiOptions = {}
): Promise<WebToolResponse> {
  if (!url) {
    return {
      output: '',
      error: 'URL is required for API operation',
      success: false,
    };
  }

  // Generate a unique request ID
  const requestId = generateCacheKey(url, method, body, options).substring(0, 8);

  // Check rate limits - use a stricter rate limit for API calls
  if (!checkRateLimit('default')) {
    return {
      output: '',
      error: 'Rate limit exceeded. Please try again later.',
      success: false,
      metadata: {
        requestId
      }
    };
  }

  try {
    // Build the URL if endpoint is provided
    const fullUrl = options.endpoint ? `${url}${options.endpoint}` : url;

    // Prepare headers with API key if provided
    const headers = options.headers || {};
    if (options.apiKey) {
      // Use common authorization header formats
      if (!headers['Authorization'] && !headers['authorization']) {
        headers['Authorization'] = `Bearer ${options.apiKey}`;
      }

      // Some APIs use custom headers for keys
      if (!headers['x-api-key']) {
        headers['x-api-key'] = options.apiKey;
      }
    }

    // Add common headers for API requests if not already set
    if (!headers['Content-Type'] && !headers['content-type'] && method !== 'GET') {
      headers['Content-Type'] = 'application/json';
    }

    if (!headers['Accept'] && !headers['accept']) {
      headers['Accept'] = 'application/json';
    }

    // Make the request with constructed parameters
    const response = await fetchUrl(fullUrl, method, body, {
      timeout: options.timeout,
      headers,
      responseType: options.responseType || 'json',
      followRedirects: options.followRedirects,
      maxRedirects: options.maxRedirects,
      cacheResults: options.cacheResults,
      cacheTtl: options.cacheTtl
    });

    return {
      ...response,
      metadata: {
        ...response.metadata,
        source: fullUrl,
        requestId
      }
    };
  } catch (error) {
    return {
      output: '',
      error: `API request error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
      metadata: {
        requestId
      }
    };
  }
}

/**
 * Performs a web search using the specified provider
 */
export async function performSearch(options: SearchOptions): Promise<WebToolResponse> {
  if (!options.query) {
    return {
      output: '',
      error: 'Query is required for search operation',
      success: false,
    };
  }

  // Generate a unique request ID
  const requestId = generateCacheKey(JSON.stringify(options), '', undefined, {}).substring(0, 8);

  // Check rate limits for the specific provider
  if (!checkRateLimit(options.provider)) {
    return {
      output: '',
      error: `Rate limit exceeded for ${options.provider} search. Please try again later.`,
      success: false,
      metadata: {
        requestId
      }
    };
  }

  // Check cache if enabled
  if (options.cacheResults !== false) {
    const cacheKey = generateCacheKey(JSON.stringify(options), '', undefined, {});
    const cachedResponse = getCachedResponse(cacheKey, options.cacheTtl);
    if (cachedResponse) {
      return cachedResponse;
    }
  }

  const numResults = options.numResults || 5;

  try {
    // Build the search query with filters if provided
    let query = options.query;

    if (options.filters) {
      // Add site restriction if provided
      if (options.filters.siteRestriction) {
        query += ` site:${options.filters.siteRestriction}`;
      }

      // Add file type restriction if provided
      if (options.filters.fileType) {
        query += ` filetype:${options.filters.fileType}`;
      }
    }

    let response: WebToolResponse;

    switch (options.provider) {
      case 'google':
        response = await googleSearch(query, options.apiKey, options.cx, numResults);
        break;
      case 'bing':
        response = await bingSearch(query, options.apiKey, numResults);
        break;
      case 'microsoft':
        // Microsoft search uses the same API as Bing
        response = await bingSearch(query, options.apiKey, numResults);
        break;
      case 'yahoo':
        // For Yahoo, we'll use a web scraping approach similar to DuckDuckGo
        response = await yahooSearch(query, numResults);
        break;
      case 'stackoverflow':
        response = await stackoverflowSearch(query, numResults);
        break;
      case 'github':
        response = await githubSearch(query, numResults);
        break;
      case 'duckduckgo':
      default:
        response = await duckduckgoSearch(query, numResults);
        break;
    }

    // Add metadata
    response.metadata = {
      ...response.metadata,
      source: options.provider,
      timestamp: Date.now(),
      requestId
    };

    // Cache the response if caching is enabled
    if (options.cacheResults !== false) {
      const cacheKey = generateCacheKey(JSON.stringify(options), '', undefined, {});
      cacheResponse(cacheKey, response);
    }

    return response;
  } catch (error) {
    return {
      output: '',
      error: `Search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
      metadata: {
        requestId
      }
    };
  }
}

/**
 * Performs a Google search using the Custom Search JSON API
 */
async function googleSearch(query: string, apiKey?: string, cx?: string, numResults: number = 5): Promise<WebToolResponse> {
  if (!apiKey) {
    return {
      output: '',
      error: 'Google search requires an API key',
      success: false,
    };
  }

  // Use provided cx or fall back to environment variable
  const customSearchId = cx || process.env.GOOGLE_SEARCH_CX;
  if (!customSearchId) {
    return {
      output: '',
      error: 'Google search requires a Custom Search Engine ID (cx)',
      success: false,
    };
  }

  try {
    const url = 'https://www.googleapis.com/customsearch/v1';
    const params = {
      key: apiKey,
      cx: customSearchId,
      q: query,
      num: Math.min(numResults, 10).toString(), // Google allows max 10 results per request
    };

    // Convert params to URL query string
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    const response = await fetchUrl(`${url}?${queryString}`, 'GET', undefined, {
      responseType: 'json',
      // Don't cache at this level since the parent function will handle caching
      cacheResults: false
    });

    if (!response.success) {
      return response;
    }

    // Parse the JSON response
    const data = JSON.parse(response.output);

    if (!data.items || data.items.length === 0) {
      return {
        output: 'No results found.',
        success: true,
      };
    }

    // Format the results
    const formattedResults = data.items
      .slice(0, numResults)
      .map((item: any, index: number) => {
        return `${index + 1}. ${item.title}
   URL: ${item.link}
   Snippet: ${item.snippet || 'No description available'}
`;
      })
      .join('\n');

    return {
      output: formattedResults,
      success: true,
    };
  } catch (error) {
    return {
      output: '',
      error: `Google search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
    };
  }
}

/**
 * Performs a StackOverflow search by scraping results
 */
async function stackoverflowSearch(query: string, numResults: number = 5): Promise<WebToolResponse> {
  try {
    // Construct the StackOverflow search URL
    const url = `https://stackoverflow.com/search?q=${encodeURIComponent(query)}`;

    const response = await scrapeWebpage(url, {
      selector: '.question-summary',
      extractContents: false,
      // Don't cache at this level since the parent function will handle caching
      cacheResults: false
    });

    if (!response.success) {
      return response;
    }

    // Parse the HTML
    const $ = cheerio.load(response.output);

    // Extract search results
    const results: { title: string; url: string; snippet: string; votes: string; answers: string }[] = [];

    $('.question-summary').each((_, element) => {
      if (results.length >= numResults) return false;

      const titleEl = $(element).find('.question-hyperlink');
      const title = titleEl.text().trim();
      const url = 'https://stackoverflow.com' + titleEl.attr('href');

      const snippet = $(element).find('.excerpt').text().trim();
      const votes = $(element).find('.vote-count-post').text().trim();
      const answers = $(element).find('.status strong').text().trim();

      results.push({
        title,
        url,
        snippet,
        votes,
        answers
      });
    });

    if (results.length === 0) {
      return {
        output: 'No results found on StackOverflow.',
        success: true,
      };
    }

    // Format the results
    const formattedResults = results
      .map((item, index) => {
        return `${index + 1}. ${item.title}
   URL: ${item.url}
   Votes: ${item.votes || '0'} | Answers: ${item.answers || '0'}
   Snippet: ${item.snippet || 'No description available'}
`;
      })
      .join('\n');

    return {
      output: formattedResults,
      success: true,
    };
  } catch (error) {
    return {
      output: '',
      error: `StackOverflow search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
    };
  }
}

/**
 * Performs a GitHub search using the GitHub API
 */
async function githubSearch(query: string, numResults: number = 5): Promise<WebToolResponse> {
  try {
    // Use GitHub's search API
    const url = `https://api.github.com/search/repositories?q=${encodeURIComponent(query)}&per_page=${numResults}`;

    const response = await fetchUrl(url, 'GET', undefined, {
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'AliveAI/1.0'
      },
      responseType: 'json',
      // Don't cache at this level since the parent function will handle caching
      cacheResults: false
    });

    if (!response.success) {
      return response;
    }

    // Parse the JSON response
    const data = JSON.parse(response.output);

    if (!data.items || data.items.length === 0) {
      return {
        output: 'No GitHub repositories found.',
        success: true,
      };
    }

    // Format the results
    const formattedResults = data.items
      .slice(0, numResults)
      .map((item: any, index: number) => {
        return `${index + 1}. ${item.full_name}
   URL: ${item.html_url}
   Stars: ${item.stargazers_count} | Forks: ${item.forks_count}
   Description: ${item.description || 'No description available'}
   Language: ${item.language || 'Not specified'}
`;
      })
      .join('\n');

    return {
      output: formattedResults,
      success: true,
    };
  } catch (error) {
    return {
      output: '',
      error: `GitHub search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
    };
  }
}

/**
 * Performs a Bing search using the Bing Web Search API
 */
async function bingSearch(query: string, apiKey?: string, numResults: number = 5): Promise<WebToolResponse> {
  if (!apiKey) {
    return {
      output: '',
      error: 'Bing search requires an API key',
      success: false,
    };
  }

  try {
    const url = 'https://api.bing.microsoft.com/v7.0/search';

    const response = await fetchUrl(`${url}?q=${encodeURIComponent(query)}&count=${numResults}`, 'GET', undefined, {
      headers: {
        'Ocp-Apim-Subscription-Key': apiKey,
      },
      responseType: 'json',
      // Don't cache at this level since the parent function will handle caching
      cacheResults: false
    });

    if (!response.success) {
      return response;
    }

    // Parse the JSON response
    const data = JSON.parse(response.output);

    if (!data.webPages || !data.webPages.value || data.webPages.value.length === 0) {
      return {
        output: 'No results found.',
        success: true,
      };
    }

    // Format the results
    const formattedResults = data.webPages.value
      .slice(0, numResults)
      .map((item: any, index: number) => {
        return `${index + 1}. ${item.name}
   URL: ${item.url}
   Snippet: ${item.snippet || 'No description available'}
`;
      })
      .join('\n');

    return {
      output: formattedResults,
      success: true,
    };
  } catch (error) {
    return {
      output: '',
      error: `Bing search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
    };
  }
}

/**
 * Performs a DuckDuckGo search by scraping results (no API key required)
 */
async function duckduckgoSearch(query: string, numResults: number = 5): Promise<WebToolResponse> {
  try {
    // DuckDuckGo doesn't have an official API, so we'll fetch the HTML page
    const url = `https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`;

    const response = await scrapeWebpage(url, {
      extractContents: false,
      // Don't cache at this level since the parent function will handle caching
      cacheResults: false
    });

    if (!response.success) {
      return response;
    }

    // Parse the HTML
    const $ = cheerio.load(response.output);

    // Extract search results
    const results: { title: string; url: string; snippet: string; source?: string }[] = [];

    $('.result').each((_, element) => {
      if (results.length >= numResults) return false;

      const titleEl = $(element).find('.result__title');
      const title = titleEl.text().trim();

      // Get URL from the href attribute, handling redirects
      let url = $(element).find('.result__url').text().trim();
      const linkHref = $(element).find('.result__a').attr('href');

      // DuckDuckGo uses redirects, extract the actual URL if possible
      if (linkHref) {
        const urlMatch = linkHref.match(/uddg=([^&]+)/);
        if (urlMatch && urlMatch[1]) {
          url = decodeURIComponent(urlMatch[1]);
        }
      }

      const snippet = $(element).find('.result__snippet').text().trim();
      const source = $(element).find('.result__url__domain').text().trim();

      results.push({
        title,
        url,
        snippet,
        source
      });
    });

    if (results.length === 0) {
      return {
        output: 'No results found.',
        success: true,
      };
    }

    // Format the results
    const formattedResults = results
      .map((item, index) => {
        return `${index + 1}. ${item.title}
   URL: ${item.url}${item.source ? `\n   Source: ${item.source}` : ''}
   Snippet: ${item.snippet || 'No description available'}
`;
      })
      .join('\n');

    return {
      output: formattedResults,
      success: true,
    };
  } catch (error) {
    return {
      output: '',
      error: `DuckDuckGo search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
    };
  }
}

/**
 * Performs a Yahoo search by scraping results (no API key required)
 */
async function yahooSearch(query: string, numResults: number = 5): Promise<WebToolResponse> {
  try {
    // Yahoo doesn't have an official API for search, so we'll scrape the search results page
    const url = `https://search.yahoo.com/search?p=${encodeURIComponent(query)}`;

    const response = await scrapeWebpage(url, {
      extractContents: false,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      // Don't cache at this level since the parent function will handle caching
      cacheResults: false
    });

    if (!response.success) {
      return response;
    }

    // Parse the HTML
    const $ = cheerio.load(response.output);

    // Extract search results
    const results: { title: string; url: string; snippet: string; source?: string }[] = [];

    // Yahoo search results are typically in divs with class 'algo'
    $('.algo').each((_, element) => {
      if (results.length >= numResults) return false;

      const titleEl = $(element).find('h3');
      const title = titleEl.text().trim();

      // Get URL from the href attribute
      const linkEl = titleEl.find('a');
      const url = linkEl.attr('href') || '#';

      // Get snippet text
      const snippet = $(element).find('.compText').text().trim();

      // Get source domain
      const source = $(element).find('.fz-ms').first().text().trim();

      results.push({
        title,
        url,
        snippet,
        source
      });
    });

    if (results.length === 0) {
      return {
        output: 'No results found or Yahoo search page structure has changed.',
        success: true,
      };
    }

    // Format the results
    const formattedResults = results
      .map((item, index) => {
        return `${index + 1}. ${item.title}
   URL: ${item.url}${item.source ? `\n   Source: ${item.source}` : ''}
   Snippet: ${item.snippet || 'No description available'}
`;
      })
      .join('\n');

    return {
      output: formattedResults,
      success: true,
    };
  } catch (error) {
    return {
      output: '',
      error: `Yahoo search error: ${error instanceof Error ? error.message : String(error)}`,
      success: false,
    };
  }
}