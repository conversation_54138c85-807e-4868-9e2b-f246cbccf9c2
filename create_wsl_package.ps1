# PowerShell script to create a WSL-compatible package for Alive AI
# This script creates a package that can be installed in WSL

# Ensure we're in the right directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

# Create a temporary directory
$tmpDir = Join-Path $env:TEMP "alive-ai-wsl-$(Get-Date -Format 'yyMMddHHmm')"
New-Item -ItemType Directory -Path $tmpDir -Force | Out-Null

Write-Host "Creating WSL package in $tmpDir"

# Create necessary directories
New-Item -ItemType Directory -Path "$tmpDir\bin" -Force | Out-Null
New-Item -ItemType Directory -Path "$tmpDir\dist" -Force | Out-Null
New-Item -ItemType Directory -Path "$tmpDir\src" -Force | Out-Null

# Copy files
Copy-Item "alive-cli\bin\alive.js" -Destination "$tmpDir\bin\" -Force
Copy-Item "alive-cli\dist\*" -Destination "$tmpDir\dist\" -Recurse -Force
Copy-Item "alive-cli\src\*" -Destination "$tmpDir\src\" -Recurse -Force
Copy-Item "README.md" -Destination "$tmpDir\ORIGINAL_README.md" -Force -ErrorAction SilentlyContinue
Copy-Item "alive-cli\WSL_README.md" -Destination "$tmpDir\README.md" -Force -ErrorAction SilentlyContinue

# Generate version based on timestamp
$version = "0.1.$(Get-Date -Format 'yyMMddHHmm')"

# Read and modify package.json
$packageJson = Get-Content "alive-cli\package.json" -Raw | ConvertFrom-Json
$packageJson.version = $version
$packageJson.name = "alive-ai-wsl"
$packageJson | ConvertTo-Json -Depth 10 | Set-Content "$tmpDir\package.json"

# Create installation script
$installScript = @'
#!/bin/bash
# Installation script for Alive AI in WSL

set -euo pipefail

# Check for Node.js
if ! command -v node &> /dev/null; then
  echo "Node.js is required but not installed."
  echo "Please install Node.js 22 or newer:"
  echo "  curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -"
  echo "  sudo apt-get install -y nodejs"
  exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 22 ]; then
  echo "Node.js 22 or newer is required. Found version: $(node -v)"
  echo "Please upgrade Node.js:"
  echo "  curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -"
  echo "  sudo apt-get install -y nodejs"
  exit 1
fi

# Install the package globally
echo "Installing Alive AI..."
npm install -g .

echo "Alive AI has been installed successfully!"
echo "You can now run 'alive' from anywhere in your WSL environment."
'@

Set-Content -Path "$tmpDir\install.sh" -Value $installScript -Encoding UTF8

# Create tarball
$tarballName = "alive-ai-wsl-$version.tar.gz"
$tarballPath = Join-Path $PWD $tarballName
$tarballCreated = $false

# Check if 7-Zip is installed
$7zPath = "$env:ProgramFiles\7-Zip\7z.exe"
if (Test-Path $7zPath) {
    # Use 7-Zip to create the tarball
    Push-Location $tmpDir
    & $7zPath a -ttar "temp.tar" *
    & $7zPath a -tgzip "$tarballPath" "temp.tar"
    Remove-Item "temp.tar"
    Pop-Location
    $tarballCreated = $true
} else {
    Write-Host "7-Zip not found. Please install 7-Zip or manually create a tarball from the files in $tmpDir"
    Write-Host "You can download 7-Zip from: https://www.7-zip.org/"
}

# Clean up temporary directory only if tarball was created
if ($tarballCreated) {
    Remove-Item -Path $tmpDir -Recurse -Force
} else {
    Write-Host "Temporary directory preserved at: $tmpDir"
}

if ($tarballCreated) {
    Write-Host "Created WSL package: $tarballPath"
    Write-Host ""
    Write-Host "To install in WSL:"
    Write-Host "1. Copy $tarballName to your WSL environment"
    Write-Host "2. Extract with: tar -xzf $tarballName"
    Write-Host "3. Run: ./install.sh"
} else {
    Write-Host ""
    Write-Host "To manually create the tarball and install in WSL:"
    Write-Host "1. Zip the contents of $tmpDir"
    Write-Host "2. Copy the zip file to your WSL environment"
    Write-Host "3. Extract the zip file"
    Write-Host "4. Run: ./install.sh"
}

# Copy installation instructions
Copy-Item "WSL_INSTALLATION.md" -Destination (Split-Path -Parent $tarballPath) -Force
Write-Host "Installation instructions are available in WSL_INSTALLATION.md"
