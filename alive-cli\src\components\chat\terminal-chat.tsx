import type { AppRollout } from "../../app.js";
import type { ApplyPatchCommand, ApprovalPolicy } from "../../approvals.js";
import type { CommandConfirmation } from "../../utils/agent/agent-loop.js";
import type { AppConfig } from "../../utils/config.js";
import type { ColorName } from "chalk";
import type { ResponseItem, ResponseInputMessageItem } from "../../types/openai";

import TerminalChatInput from "./terminal-chat-input.js";
import TerminalChatPastRollout from "./terminal-chat-past-rollout.js";
import { TerminalChatToolCallCommand } from "./terminal-chat-tool-call-command.js";
import TerminalMessageHistory from "./terminal-message-history.js";
import { formatCommandForDisplay } from "../../format-command.js";
import { useConfirmation } from "../../hooks/use-confirmation.js";
import { useTerminalSize } from "../../hooks/use-terminal-size.js";
import { AgentLoop } from "../../utils/agent/agent-loop.js";
import { ReviewDecision } from "../../utils/agent/review.js";
import { generateCompactSummary } from "../../utils/compact-summary.js";
import { saveConfig } from "../../utils/config.js";
import { extractAppliedPatches as _extractAppliedPatches } from "../../utils/extract-applied-patches.js";
import { getGitDiff } from "../../utils/get-diff.js";
import { createInputItem } from "../../utils/input-utils.js";
import { log } from "../../utils/logger/log.js";
import {
  getAvailableModels,
  calculateContextPercentRemaining,
  uniqueById,
} from "../../utils/model-utils.js";
import { createOpenAIClient } from "../../utils/openai-client.js";
import { shortCwd } from "../../utils/short-path.js";
import { saveRollout } from "../../utils/storage/save-rollout.js";
import { CLI_VERSION } from "../../version.js";
import { actionTracker } from "../../utils/action-tracker.js";
import ApprovalModeOverlay from "../approval-mode-overlay.js";
import DiffOverlay from "../diff-overlay.js";
import HelpOverlay from "../help-overlay.js";
import HistoryOverlay from "../history-overlay.js";
import ModelOverlay from "../model-overlay.js";
import SessionsOverlay from "../sessions-overlay.js";
import ThemeOverlay from "../theme-overlay.js";
import HealthOverlay from "../health-overlay.js";
import chalk from "chalk";
import fs from "fs/promises";
import { Box, Text } from "ink";
import { spawn } from "node:child_process";
import React, { useEffect, useMemo, useRef, useState, createContext } from "react";
import { inspect } from "util";
import { useInterval } from "../../utils/hooks/use-interval.js";

export type OverlayModeType =
  | "none"
  | "history"
  | "sessions"
  | "model"
  | "approval"
  | "help"
  | "diff"
  | "theme"
  | "health";

type Props = {
  config: AppConfig;
  prompt?: string;
  imagePaths?: Array<string>;
  approvalPolicy: ApprovalPolicy;
  additionalWritableRoots: ReadonlyArray<string>;
  fullStdout: boolean;
};

const colorsByPolicy: Record<ApprovalPolicy, ColorName | undefined> = {
  "suggest": undefined,
  "auto-edit": "greenBright",
  "full-auto": "green",
};

// Create a ThemeContext to share theme across components
export const ThemeContext = createContext<{
  theme: string;
  setTheme: (theme: string) => void;
}>({
  theme: "default",
  setTheme: () => {},
});

/**
 * Generates an explanation for a shell command using the OpenAI API.
 *
 * @param command The command to explain
 * @param model The model to use for generating the explanation
 * @param flexMode Whether to use the flex-mode service tier
 * @param config The configuration object
 * @returns A human-readable explanation of what the command does
 */
async function generateCommandExplanation(
  command: Array<string>,
  model: string,
  flexMode: boolean,
  config: AppConfig,
): Promise<string> {
  try {
    // Create a temporary OpenAI client
    const oai = createOpenAIClient(config);

    // Format the command for display
    const commandForDisplay = formatCommandForDisplay(command);

    // Create a prompt that asks for an explanation with a more detailed system prompt
    const response = await oai.chat.completions.create({
      model,
      ...(flexMode ? { service_tier: "flex" } : {}),
      messages: [
        {
          role: "system",
          content:
            "You are an expert in shell commands and terminal operations. Your task is to provide detailed, accurate explanations of shell commands that users are considering executing. Break down each part of the command, explain what it does, identify any potential risks or side effects, and explain why someone might want to run it. Be specific about what files or systems will be affected. If the command could potentially be harmful, make sure to clearly highlight those risks.",
        },
        {
          role: "user",
          content: `Please explain this shell command in detail: \`${commandForDisplay}\`\n\nProvide a structured explanation that includes:\n1. A brief overview of what the command does\n2. A breakdown of each part of the command (flags, arguments, etc.)\n3. What files, directories, or systems will be affected\n4. Any potential risks or side effects\n5. Why someone might want to run this command\n\nBe specific and technical - this explanation will help the user decide whether to approve or reject the command.`,
        },
      ],
    });

    // Extract the explanation from the response
    const explanation =
      response.choices[0]?.message.content || "Unable to generate explanation.";
    return explanation;
  } catch (error) {
    log(`Error generating command explanation: ${error}`);

    let errorMessage = "Unable to generate explanation due to an error.";
    if (error instanceof Error) {
      errorMessage = `Unable to generate explanation: ${error.message}`;

      // If it's an API error, check for more specific information
      if ("status" in error && typeof error.status === "number") {
        // Handle API-specific errors
        if (error.status === 401) {
          errorMessage =
            "Unable to generate explanation: API key is invalid or expired.";
        } else if (error.status === 429) {
          errorMessage =
            "Unable to generate explanation: Rate limit exceeded. Please try again later.";
        } else if (error.status >= 500) {
          errorMessage =
            "Unable to generate explanation: OpenAI service is currently unavailable. Please try again later.";
        }
      }
    }

    return errorMessage;
  }
}

export default function TerminalChat({
  config,
  prompt: _initialPrompt,
  imagePaths: _initialImagePaths,
  approvalPolicy: initialApprovalPolicy,
  additionalWritableRoots,
  fullStdout,
}: Props): React.ReactElement {
  const notify = Boolean(config.notify);
  const [model, setModel] = useState<string>(config.model);
  const [provider, setProvider] = useState<string>(config.provider || "openai");
  const [lastResponseId, setLastResponseId] = useState<string | null>(null);
  const [items, setItems] = useState<Array<ResponseItem>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [approvalPolicy, setApprovalPolicy] = useState<ApprovalPolicy>(
    initialApprovalPolicy,
  );
  const [thinkingSeconds, setThinkingSeconds] = useState(0);
  const [fileSystemStatus, setFileSystemStatus] = useState<{
    status: 'idle' | 'reading' | 'writing' | 'error';
    message: string;
    path?: string;
  }>({ status: 'idle', message: '' });
  
  // Theme state - initialize from config if available
  const [theme, setTheme] = useState<string>(config.theme || "default");

  // Helper function to extract file paths from commands
  const extractFilePath = (commandStr: string): string | undefined => {
    // Look for quoted paths first
    const quotedMatch = commandStr.match(/['"]([^'"]+)['"]/);
    if (quotedMatch) return quotedMatch[1];

    // Look for common command patterns
    const parts = commandStr.split(' ');
    if (parts.length < 2) return undefined;

    // For commands like cat, ls, etc. the path is usually the last argument
    // that doesn't start with a dash (to exclude flags)
    for (let i = parts.length - 1; i >= 1; i--) {
      if (!parts[i].startsWith('-') && parts[i].trim() !== '') {
        return parts[i];
      }
    }

    return undefined;
  };

  const handleCompact = async () => {
    setLoading(true);
    try {
      const summary = await generateCompactSummary(
        items as any,
        model,
        Boolean(config.flexMode),
        config,
      );
      setItems([
        {
          id: `compact-${Date.now()}`,
          type: "message",
          role: "assistant",
          content: [{ type: "output_text", text: summary }],
        } as ResponseItem,
      ]);
    } catch (err) {
      setItems((prev) => [
        ...prev,
        {
          id: `compact-error-${Date.now()}`,
          type: "message",
          role: "system",
          content: [
            { type: "input_text", text: `Failed to compact context: ${err}` },
          ],
        } as ResponseItem,
      ]);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle theme changes
  const handleThemeChange = (themeId: string) => {
    setTheme(themeId);
    // Save theme to config
    try {
      const updatedConfig = { ...config, theme: themeId };
      saveConfig(updatedConfig);
      
      // Add a system message to confirm theme change
      setItems((prev) => [
        ...prev,
        {
          id: `theme-change-${Date.now()}`,
          type: "message",
          role: "system",
          content: [
            { 
              type: "input_text", 
              text: `Theme changed to ${themeId}.` 
            },
          ],
        } as ResponseItem,
      ]);
    } catch (err) {
      log(`Error saving theme: ${err}`);
      setItems((prev) => [
        ...prev,
        {
          id: `theme-error-${Date.now()}`,
          type: "message",
          role: "system",
          content: [
            { 
              type: "input_text", 
              text: `Error changing theme: ${err}` 
            },
          ],
        } as ResponseItem,
      ]);
    }
  };

  // Load theme from config on startup
  useEffect(() => {
    if (config.theme) {
      setTheme(config.theme);
    }
  }, [config.theme]);

  const {
    requestConfirmation,
    confirmationPrompt,
    explanation,
    submitConfirmation,
  } = useConfirmation();
  const [overlayMode, setOverlayMode] = useState<OverlayModeType>("none");
  const [viewRollout, setViewRollout] = useState<AppRollout | null>(null);

  // Store the diff text when opening the diff overlay so the view isn't
  // recomputed on every re‑render while it is open.
  // diffText is passed down to the DiffOverlay component. The setter is
  // currently unused but retained for potential future updates. Prefix with
  // an underscore so eslint ignores the unused variable.
  const [diffText, _setDiffText] = useState<string>("");

  const [initialPrompt, setInitialPrompt] = useState(_initialPrompt);
  const [initialImagePaths, setInitialImagePaths] =
    useState(_initialImagePaths);

  const PWD = React.useMemo(() => shortCwd(), []);

  // Keep a single AgentLoop instance alive across renders;
  // recreate only when model/instructions/approvalPolicy change.
  const agentRef = React.useRef<AgentLoop>();
  const [, forceUpdate] = React.useReducer((c) => c + 1, 0); // trigger re‑render

  // ────────────────────────────────────────────────────────────────
  // DEBUG: log every render w/ key bits of state
  // ────────────────────────────────────────────────────────────────
  log(
    `render - agent? ${Boolean(agentRef.current)} loading=${loading} items=${
      items.length
    }`,
  );

  useEffect(() => {
    // Skip recreating the agent if awaiting a decision on a pending confirmation.
    if (confirmationPrompt != null) {
      log("skip AgentLoop recreation due to pending confirmationPrompt");
      return;
    }

    log("creating NEW AgentLoop");
    log(
      `model=${model} provider=${provider} instructions=${Boolean(
        config.instructions,
      )} approvalPolicy=${approvalPolicy}`,
    );

    // Tear down any existing loop before creating a new one.
    agentRef.current?.terminate();

    const sessionId = crypto.randomUUID();
    agentRef.current = new AgentLoop({
      model,
      provider,
      config,
      instructions: config.instructions,
      approvalPolicy,
      disableResponseStorage: config.disableResponseStorage,
      additionalWritableRoots,
      onLastResponseId: setLastResponseId,
      onItem: (item) => {
        log(`onItem: ${JSON.stringify(item)}`);

        // Update file system status based on function calls
        if ((item as any).type === 'function_call' || (item as any).type === 'local_shell_call') {
          // Check for file operations in the function name or command
          const functionName = (item as any).type === 'function_call' ? (item as any).name || '' : '';
          
          // Safely extract command string based on item type
          let commandStr = '';
          if ((item as any).type === 'function_call') {
            const toolCall = (item as any);
            commandStr = toolCall.function?.arguments ? 
              JSON.stringify(toolCall.function.arguments) : '';
          } else if ((item as any).type === 'local_shell_call') {
            const shellCall = (item as any);
            commandStr = shellCall.action?.command || '';
            if (Array.isArray(commandStr)) {
              commandStr = commandStr.join(' ');
            }
          }

          // Check for file read operations
          if (functionName.includes('read') || commandStr.includes('cat ') || commandStr.includes('ls ')) {
            const path = extractFilePath(commandStr);
            setFileSystemStatus({
              status: 'reading',
              message: 'Reading file',
              path
            });

            // Add to action tracker
            actionTracker.addFileSystemAction('read', path);

            // Start the action immediately if there's no current action
            if (!actionTracker.getCurrentAction()) {
              actionTracker.startNextAction();
            }

            // Reset status after 3 seconds
            setTimeout(() => {
              setFileSystemStatus({ status: 'idle', message: '' });
            }, 3000);
          }

          // Check for file write operations
          if (functionName.includes('write') || commandStr.includes('echo ') ||
              commandStr.includes('> ') || commandStr.includes('mkdir ') ||
              commandStr.includes('touch ') || commandStr.includes('apply_patch')) {
            const path = extractFilePath(commandStr);
            setFileSystemStatus({
              status: 'writing',
              message: 'Writing to file',
              path
            });

            // Add to action tracker
            actionTracker.addFileSystemAction('write', path);

            // Start the action immediately if there's no current action
            if (!actionTracker.getCurrentAction()) {
              actionTracker.startNextAction();
            }

            // Reset status after 3 seconds
            setTimeout(() => {
              setFileSystemStatus({ status: 'idle', message: '' });
            }, 3000);
          }
        }

        // Check for error responses related to file operations
        if (item.type === 'message' && item.role === 'assistant') {
          const content = item.content || [];
          for (const part of content) {
            if (part.type === 'output_text' && typeof part.text === 'string') {
              // Check for common file error messages with improved detection
              const errorText = part.text.toLowerCase();
              const isErrorMessage =
                (errorText.includes('error') ||
                 errorText.includes('failed') ||
                 errorText.includes('cannot') ||
                 errorText.includes('not found') ||
                 errorText.includes('no such') ||
                 errorText.includes('permission denied')) &&
                (errorText.includes('file') ||
                 errorText.includes('directory') ||
                 errorText.includes('path') ||
                 errorText.includes('permission') ||
                 errorText.includes('access'));

              if (isErrorMessage) {
                // Extract the file path using improved regex patterns
                let path: string | undefined;

                // Try to find paths in quotes
                const quotedPathMatch = part.text.match(/['"]([^'"]+)['"]/);
                if (quotedPathMatch) {
                  path = quotedPathMatch[1];
                } else {
                  // Try to find paths with common patterns
                  const pathPatterns = [
                    /(?:file|path|directory)(?:\s+|:\s*)['"]?([^'":\s]+)['"]?/i,
                    /['"]?([^'":\s]+\.[a-zA-Z0-9]+)['"]?/,
                    /(?:\/[^\/\s]+)+/
                  ];

                  for (const pattern of pathPatterns) {
                    const match = part.text.match(pattern);
                    if (match && match[1]) {
                      path = match[1];
                      break;
                    }
                  }
                }

                // Extract a more specific error message
                let errorMessage = 'File operation failed';
                const errorPatterns = [
                  /(?:error|failed|cannot)(?:\s*:\s*)([^.]+)/i,
                  /(?:no such file or directory|permission denied|cannot access|file not found)([^.]+)?/i
                ];

                for (const pattern of errorPatterns) {
                  const match = part.text.match(pattern);
                  if (match) {
                    errorMessage = match[0];
                    break;
                  }
                }

                // Update file system status with detailed error
                setFileSystemStatus({
                  status: 'error',
                  message: errorMessage,
                  path
                });

                // Add to action tracker with detailed error information
                const errorAction = actionTracker.addFileSystemAction('error', path, part.text);

                // Complete current action with failure if it exists
                const currentAction = actionTracker.getCurrentAction();
                if (currentAction &&
                    (currentAction.type === 'file_read' || currentAction.type === 'file_write')) {
                  actionTracker.updateCurrentActionDetails({
                    error: errorMessage
                  });
                  actionTracker.completeCurrentAction(true);
                }

                // Start the error action if not already started
                if (!actionTracker.getCurrentAction()) {
                  actionTracker.startNextAction();
                }

                // Keep error visible a bit longer
                setTimeout(() => {
                  setFileSystemStatus({ status: 'idle', message: '' });
                }, 8000);
              }
            }
          }
        }

        setItems((prev) => {
          const updated = uniqueById([...prev, item as any]);
          saveRollout(sessionId, updated as any);
          return updated;
        });
      },
      onLoading: setLoading,
      getCommandConfirmation: async (
        command: Array<string>,
        applyPatch: ApplyPatchCommand | undefined,
      ): Promise<CommandConfirmation> => {
        log(`getCommandConfirmation: ${command}`);
        const commandForDisplay = formatCommandForDisplay(command);

        // Add command execution action to tracker with enhanced details
        actionTracker.addCommandExecutionAction(commandForDisplay);

        // First request for confirmation
        let { decision: review, customDenyMessage } = await requestConfirmation(
          <TerminalChatToolCallCommand commandForDisplay={commandForDisplay} />,
        );

        // If the user wants an explanation, generate one and ask again.
        if (review === ReviewDecision.EXPLAIN) {
          log(`Generating explanation for command: ${commandForDisplay}`);
          const explanation = await generateCommandExplanation(
            command,
            model,
            Boolean(config.flexMode),
            config,
          );
          log(`Generated explanation: ${explanation}`);

          // Ask for confirmation again, but with the explanation.
          const confirmResult = await requestConfirmation(
            <TerminalChatToolCallCommand
              commandForDisplay={commandForDisplay}
              explanation={explanation}
            />,
          );

          // Update the decision based on the second confirmation.
          review = confirmResult.decision;
          customDenyMessage = confirmResult.customDenyMessage;
        }

        log(`Command confirmation: ${review}`);

        // TODO: Handle file diff approval logic
        if (review === ReviewDecision.YES) {
          actionTracker.updateCurrentActionDetails({
            status: 'Approved'
          });
          actionTracker.completeCurrentAction(false);
          return { review: ReviewDecision.YES };
        }

        actionTracker.updateCurrentActionDetails({
          status: 'Rejected',
          error: customDenyMessage || 'Command rejected by user'
        });
        actionTracker.completeCurrentAction(true);

        return { review: ReviewDecision.NO_CONTINUE, customDenyMessage };
      },
    });

    // Force a render so JSX below can "see" the freshly created agent.
    forceUpdate();

    log(`AgentLoop created: ${inspect(agentRef.current, { depth: 1 })}`);

    return () => {
      log("terminating AgentLoop");
      agentRef.current?.terminate();
      agentRef.current = undefined;
      forceUpdate(); // re‑render after teardown too
    };
    // We intentionally omit 'approvalPolicy' and 'confirmationPrompt' from the deps
    // so switching modes or showing confirmation dialogs doesn't tear down the loop.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [model, provider, config, requestConfirmation, additionalWritableRoots]);

  // Whenever loading starts/stops, reset or start a timer — but pause the
  // timer while a confirmation overlay is displayed so we don't trigger a
  // re‑render every second during apply_patch reviews.
  useEffect(() => {
    let handle: ReturnType<typeof setInterval> | null = null;
    // Only tick the "thinking…" timer when the agent is actually processing
    // a request *and* the user is not being asked to review a command.
    if (loading && confirmationPrompt == null) {
      setThinkingSeconds(0);

      // Add a thinking action to the tracker when loading starts
      const thinkingAction = actionTracker.addThinkingAction('Processing your request');

      // Start the thinking action
      actionTracker.startNextAction();

      // Add some realistic sub-actions to show progress
      setTimeout(() => {
        const currentAction = actionTracker.getCurrentAction();
        if (currentAction && currentAction.type === 'thinking') {
          actionTracker.addSubAction({
            description: 'Analyzing context',
            type: 'processing'
          });
        }
      }, 2000);

      setTimeout(() => {
        const currentAction = actionTracker.getCurrentAction();
        if (currentAction && currentAction.type === 'thinking') {
          actionTracker.addSubAction({
            description: 'Retrieving relevant information',
            type: 'processing'
          });
        }
      }, 5000);

      setTimeout(() => {
        const currentAction = actionTracker.getCurrentAction();
        if (currentAction && currentAction.type === 'thinking') {
          actionTracker.addSubAction({
            description: 'Generating response',
            type: 'processing'
          });
        }
      }, 10000);

      // Update progress periodically
      handle = setInterval(() => {
        setThinkingSeconds((s) => {
          // Just increment the seconds counter - progress is handled by the thinking action
          return s + 1;
        });
      }, 1000);
    } else {
      // Complete the thinking action when loading stops
      const currentAction = actionTracker.getCurrentAction();
      if (currentAction && currentAction.type === 'thinking') {
        actionTracker.completeCurrentAction();
      }

      if (handle) {
        clearInterval(handle);
      }
      setThinkingSeconds(0);
    }
    return () => {
      if (handle) {
        clearInterval(handle);
      }
    };
  }, [loading, confirmationPrompt]);

  // Notify desktop with a preview when an assistant response arrives.
  const prevLoadingRef = useRef<boolean>(false);
  useEffect(() => {
    // Only notify when notifications are enabled.
    if (!notify) {
      prevLoadingRef.current = loading;
      return;
    }

    if (
      prevLoadingRef.current &&
      !loading &&
      confirmationPrompt == null &&
      items.length > 0
    ) {
      if (process.platform === "darwin") {
        // find the last assistant message
        const assistantMessages = items.filter(
          (i) => i.type === "message" && i.role === "assistant",
        );
        const last = assistantMessages[assistantMessages.length - 1];
        if (last) {
          const text = last.content
            .map((c) => {
              if (c.type === "output_text") {
                return c.text;
              }
              return "";
            })
            .join("")
            .trim();
          const preview = text.replace(/\n/g, " ").slice(0, 100);
          const safePreview = preview.replace(/"/g, '\\"');
          const title = "Alive AI CLI";
          const cwd = PWD;
          spawn("osascript", [
            "-e",
            `display notification "${safePreview}" with title "${title}" subtitle "${cwd}" sound name "Ping"`,
          ]);
        }
      }
    }
    prevLoadingRef.current = loading;
  }, [notify, loading, confirmationPrompt, items, PWD]);

  // Let's also track whenever the ref becomes available.
  const agent = agentRef.current;
  useEffect(() => {
    log(`agentRef.current is now ${Boolean(agent)}`);
  }, [agent]);

  // ---------------------------------------------------------------------
  // Dynamic layout constraints – keep total rendered rows <= terminal rows
  // ---------------------------------------------------------------------

  const { rows: terminalRows } = useTerminalSize();

  useEffect(() => {
    const processInitialInputItems = async () => {
      if (
        (!initialPrompt || initialPrompt.trim() === "") &&
        (!initialImagePaths || initialImagePaths.length === 0)
      ) {
        return;
      }
      const inputItems = [
        await createInputItem(initialPrompt || "", initialImagePaths || []),
      ];
      // Clear them to prevent subsequent runs.
      setInitialPrompt("");
      setInitialImagePaths([]);
      agent?.run(inputItems);
    };
    processInitialInputItems();
  }, [agent, initialPrompt, initialImagePaths]);

  // ────────────────────────────────────────────────────────────────
  // In-app warning if CLI --model isn't in fetched list
  // ────────────────────────────────────────────────────────────────
  useEffect(() => {
    (async () => {
      const available = await getAvailableModels(provider);
      if (model && available.length > 0 && !available.includes(model)) {
        setItems((prev) => [
          ...prev,
          {
            id: `unknown-model-${Date.now()}`,
            type: "message",
            role: "system",
            content: [
              {
                type: "input_text",
                text: `Warning: model "${model}" is not in the list of available models for provider "${provider}".`,
              },
            ],
          },
        ]);
      }
    })();
    // run once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Just render every item in order, no grouping/collapse.
  const lastMessageBatch = items.map((item) => ({ item }));
  const groupCounts: Record<string, number> = {};
  const userMsgCount = items.filter(
    (i) => i.type === "message" && i.role === "user",
  ).length;

  const contextLeftPercent = useMemo(
    () => calculateContextPercentRemaining(items as any, model),
    [items, model],
  );

  if (viewRollout) {
    return (
      <TerminalChatPastRollout
        fileOpener={config.fileOpener}
        session={viewRollout.session}
        items={viewRollout.items}
      />
    );
  }

  // File System Status Indicator Component
  const FileSystemStatusIndicator = () => {
    if (fileSystemStatus.status === 'idle') {
      return null;
    }

    let statusColor = 'gray';
    let statusIcon = '';
    let statusBgColor = undefined;
    let animationFrames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    const [animationFrame, setAnimationFrame] = useState(0);

    // Animate the spinner for active operations
    useInterval(() => {
      if (fileSystemStatus.status !== 'error') {
        setAnimationFrame((prev) => (prev + 1) % animationFrames.length);
      }
    }, 100);

    switch (fileSystemStatus.status) {
      case 'reading':
        statusColor = 'blueBright';
        statusIcon = '📖';
        break;
      case 'writing':
        statusColor = 'greenBright';
        statusIcon = '💾';
        break;
      case 'error':
        statusColor = 'redBright';
        statusIcon = '❌';
        statusBgColor = 'red';
        break;
    }

    // Add file system action to the action tracker if not already tracked
    useEffect(() => {
      if (fileSystemStatus.status !== 'idle') {
        const currentAction = actionTracker.getCurrentAction();

        // Only add a new action if the current action is not already tracking this file operation
        if (!currentAction ||
            (currentAction.type !== 'file_read' && currentAction.type !== 'file_write') ||
            currentAction.details?.path !== fileSystemStatus.path) {

          const action = actionTracker.addFileSystemAction(
            fileSystemStatus.status === 'reading' ? 'read' :
            fileSystemStatus.status === 'writing' ? 'write' : 'error',
            fileSystemStatus.path
          );

          // For error status, add more detailed error information
          if (fileSystemStatus.status === 'error' && fileSystemStatus.message) {
            actionTracker.updateCurrentActionDetails({
              error: fileSystemStatus.message
            });
          }

          // Start the action if there's no current action
          if (!actionTracker.getCurrentAction()) {
            actionTracker.startNextAction();
          }
        }
      }
    }, [fileSystemStatus]);

    // Enhanced display for error status
    if (fileSystemStatus.status === 'error') {
      return (
        <Box
          borderStyle="round"
          borderColor={statusColor}
          paddingX={1}
          paddingY={0}
          marginBottom={1}
          width="100%"
          flexDirection="column"
        >
          <Box>
            <Text backgroundColor={statusBgColor} color="white" bold>
              {statusIcon} File Operation Failed
            </Text>
          </Box>
          <Box paddingLeft={1} paddingTop={0}>
            <Text color={statusColor}>
              {fileSystemStatus.message}
            </Text>
          </Box>
          {fileSystemStatus.path && (
            <Box paddingLeft={1} paddingTop={0}>
              <Text dimColor>
                Path: {fileSystemStatus.path}
              </Text>
            </Box>
          )}
          <Box paddingTop={0}>
            <Text dimColor>
              This error will be automatically cleared in a few seconds.
            </Text>
          </Box>
        </Box>
      );
    }

    // Standard display for non-error statuses
    return (
      <Box
        borderStyle="round"
        borderColor={statusColor}
        paddingX={1}
        marginBottom={1}
        width="auto"
      >
        <Text color={statusColor}>
          {statusIcon} {animationFrames[animationFrame]} {fileSystemStatus.message} {fileSystemStatus.path ? `(${fileSystemStatus.path})` : ''}
        </Text>
      </Box>
    );
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme: handleThemeChange }}>
      <Box flexDirection="column">
        <Box flexDirection="column">
          {/* File System Status Indicator */}
          <FileSystemStatusIndicator />

          {agent ? (
            <TerminalMessageHistory
              setOverlayMode={setOverlayMode}
              batch={lastMessageBatch}
              groupCounts={groupCounts}
              items={items}
              userMsgCount={userMsgCount}
              confirmationPrompt={confirmationPrompt}
              loading={loading}
              thinkingSeconds={thinkingSeconds}
              fullStdout={fullStdout}
              headerProps={{
                terminalRows,
                version: CLI_VERSION,
                PWD,
                model,
                provider,
                approvalPolicy,
                colorsByPolicy,
                agent,
                initialImagePaths,
                flexModeEnabled: Boolean(config.flexMode),
              }}
              fileOpener={config.fileOpener}
            />
          ) : (
            <Box>
              <Text color="gray">Initializing agent…</Text>
            </Box>
          )}
          {overlayMode === "none" && agent && (
            <TerminalChatInput
              loading={loading}
              setItems={setItems}
              isNew={Boolean(items.length === 0)}
              setLastResponseId={setLastResponseId}
              confirmationPrompt={confirmationPrompt}
              explanation={explanation}
              submitConfirmation={(
                decision: ReviewDecision,
                customDenyMessage?: string,
              ) =>
                submitConfirmation({
                  decision,
                  customDenyMessage,
                })
              }
              contextLeftPercent={contextLeftPercent}
              openOverlay={() => setOverlayMode("history")}
              openModelOverlay={() => setOverlayMode("model")}
              openApprovalOverlay={() => setOverlayMode("approval")}
              openHelpOverlay={() => setOverlayMode("help")}
              openSessionsOverlay={() => setOverlayMode("sessions")}
              openThemeOverlay={() => setOverlayMode("theme")}
              openHealthOverlay={() => setOverlayMode("health")}
              openDiffOverlay={() => {
                const { isGitRepo, diff } = getGitDiff();
                let text: string;
                if (isGitRepo) {
                  text = diff;
                } else {
                  text = "`/diff` — _not inside a git repository_";
                }
                setItems((prev) => [
                  ...prev,
                  {
                    id: `diff-${Date.now()}`,
                    type: "message",
                    role: "system",
                    content: [{ type: "input_text", text }],
                  },
                ]);
                // Ensure no overlay is shown.
                setOverlayMode("none");
              }}
              onCompact={handleCompact}
              active={overlayMode === "none"}
              interruptAgent={() => {
                if (!agent) {
                  return;
                }
                log(
                  "TerminalChat: interruptAgent invoked – calling agent.cancel()",
                );
                agent.cancel();
                setLoading(false);

                // Clear action tracker when interrupted
                actionTracker.clearAll();

                // Add a system message to indicate the interruption
                setItems((prev) => [
                  ...prev,
                  {
                    id: `interrupt-${Date.now()}`,
                    type: "message",
                    role: "system",
                    content: [
                      {
                        type: "input_text",
                        text: "⏹️  Execution interrupted by user. You can continue typing.",
                      },
                    ],
                  },
                ]);
              }}
              submitInput={(inputs) => {
                agent.run(inputs, lastResponseId || "");
                return {};
              }}
              items={items}
              thinkingSeconds={thinkingSeconds}
            />
          )}
          {overlayMode === "history" && (
            <HistoryOverlay items={items} onExit={() => setOverlayMode("none")} />
          )}
          {overlayMode === "sessions" && (
            <SessionsOverlay
              onView={async (p) => {
                try {
                  const txt = await fs.readFile(p, "utf-8");
                  const data = JSON.parse(txt) as AppRollout;
                  setViewRollout(data);
                  setOverlayMode("none");
                } catch {
                  setOverlayMode("none");
                }
              }}
              onResume={(p) => {
                setOverlayMode("none");
                setInitialPrompt(`Resume this session: ${p}`);
              }}
              onExit={() => setOverlayMode("none")}
            />
          )}
          {overlayMode === "model" && (
            <ModelOverlay
              currentModel={model}
              providers={config.providers}
              currentProvider={provider}
              hasLastResponse={Boolean(lastResponseId)}
              onSelect={(allModels, newModel) => {
                log(
                  "TerminalChat: interruptAgent invoked – calling agent.cancel()",
                );
                if (!agent) {
                  log("TerminalChat: agent is not ready yet");
                }
                agent?.cancel();
                setLoading(false);

                if (!allModels?.includes(newModel)) {
                  // eslint-disable-next-line no-console
                  console.error(
                    chalk.bold.red(
                      `Model "${chalk.yellow(
                        newModel,
                      )}" is not available for provider "${chalk.yellow(
                        provider,
                      )}".`,
                    ),
                  );
                  return;
                }

                setModel(newModel);
                setLastResponseId((prev) =>
                  prev && newModel !== model ? null : prev,
                );

                // Clear action tracker when switching models
                actionTracker.clearAll();

                // Save model to config
                saveConfig({
                  ...config,
                  model: newModel,
                  provider: provider,
                });

                setItems((prev) => [
                  ...prev,
                  {
                    id: `switch-model-${Date.now()}`,
                    type: "message",
                    role: "system",
                    content: [
                      {
                        type: "input_text",
                        text: `Switched model to ${newModel}`,
                      },
                    ],
                  },
                ]);

                setOverlayMode("none");
              }}
              onSelectProvider={async (newProvider) => {
                log(
                  "TerminalChat: interruptAgent invoked – calling agent.cancel()",
                );
                if (!agent) {
                  log("TerminalChat: agent is not ready yet");
                }
                agent?.cancel();
                setLoading(false);

                const normalizedProvider = newProvider.toLowerCase();

                // Get appropriate default model for the new provider
                let defaultModel = model;

                // For DeepSeek, use a DeepSeek-specific model if current model isn't compatible
                if (normalizedProvider === "deepseek") {
                  const deepseekModels = [
                    "deepseek-chat",
                    "deepseek-coder",
                    "deepseek-llm",
                    "deepseek-v2",
                    "alive-mini-latest"
                  ];

                  // If current model isn't in the DeepSeek models list, use deepseek-chat as default
                  if (!deepseekModels.includes(model)) {
                    defaultModel = "deepseek-chat";
                  }
                }

                // Save provider to config.
                const updatedConfig = {
                  ...config,
                  provider: newProvider,
                  model: defaultModel,
                };
                saveConfig(updatedConfig);

                setProvider(newProvider);
                setModel(defaultModel);
                setLastResponseId((prev) =>
                  prev && newProvider !== provider ? null : prev,
                );

                setItems((prev) => [
                  ...prev,
                  {
                    id: `switch-provider-${Date.now()}`,
                    type: "message",
                    role: "system",
                    content: [
                      {
                        type: "input_text",
                        text: `Switched provider to ${newProvider} with model ${defaultModel}`,
                      },
                    ],
                  },
                ]);

                // Don't close the overlay so user can select a model for the new provider
                // setOverlayMode("none");
              }}
              onExit={() => setOverlayMode("none")}
            />
          )}

          {overlayMode === "approval" && (
            <ApprovalModeOverlay
              currentMode={approvalPolicy}
              onSelect={(newMode) => {
                // Update approval policy without cancelling an in-progress session.
                if (newMode === approvalPolicy) {
                  return;
                }

                setApprovalPolicy(newMode as ApprovalPolicy);
                if (agentRef.current) {
                  (
                    agentRef.current as unknown as {
                      approvalPolicy: ApprovalPolicy;
                    }
                  ).approvalPolicy = newMode as ApprovalPolicy;
                }
                setItems((prev) => [
                  ...prev,
                  {
                    id: `switch-approval-${Date.now()}`,
                    type: "message",
                    role: "system",
                    content: [
                      {
                        type: "input_text",
                        text: `Switched approval mode to ${newMode}`,
                      },
                    ],
                  },
                ]);

                setOverlayMode("none");
              }}
              onExit={() => setOverlayMode("none")}
            />
          )}

          {overlayMode === "help" && (
            <HelpOverlay onExit={() => setOverlayMode("none")} />
          )}

          {overlayMode === "diff" && (
            <DiffOverlay
              diffText={diffText}
              onExit={() => setOverlayMode("none")}
            />
          )}
          
          {overlayMode === "theme" && (
            <ThemeOverlay
              currentTheme={theme}
              onThemeChange={handleThemeChange}
              onExit={() => setOverlayMode("none")}
            />
          )}
          
          {overlayMode === "health" && (
            <HealthOverlay
              onExit={() => setOverlayMode("none")}
            />
          )}
        </Box>
      </Box>
    </ThemeContext.Provider>
  );
}
