import type { AppConfig } from "./config.js";

import {
  getBaseUrl,
  getApi<PERSON>ey,
  AZURE_OPENAI_API_VERSION,
  OPENAI_TIMEOUT_MS,
  OPENAI_ORGANIZATION,
  OPENAI_PROJECT,
} from "./config.js";
import OpenAI, { AzureOpenAI } from "openai";
import { providers } from "./providers.js";
import chalk from "chalk";

type OpenAIClientConfig = {
  provider: string;
};

/**
 * Creates an OpenAI client instance based on the provided configuration.
 * Handles both standard OpenAI and Azure OpenAI configurations.
 *
 * @param config The configuration containing provider information
 * @returns An instance of either OpenAI or AzureOpenAI client
 */
export function createOpenAIClient(
  config: OpenAIClientConfig | AppConfig,
): OpenAI | AzureOpenAI {
  const provider = config.provider?.toLowerCase() || "openai";
  const apiKey = getApiKey(provider);
  
  // If no API key is found, try to provide a helpful error
  if (!apiKey && provider !== "ollama") {
    const providerInfo = providers[provider];
    const envKey = providerInfo?.envKey || `${provider.toUpperCase()}_API_KEY`;
    
    // Instead of throwing, we'll log a warning and use a placeholder key
    // This will fail when making actual API calls, but allows us to create the client
    // eslint-disable-next-line no-console
    console.warn(chalk.yellow(
      `Warning: No API key found for provider "${provider}". ` +
      `Set the ${chalk.bold(envKey)} environment variable.`
    ));
  }
  
  const headers: Record<string, string> = {};
  if (OPENAI_ORGANIZATION) {
    headers["OpenAI-Organization"] = OPENAI_ORGANIZATION;
  }
  if (OPENAI_PROJECT) {
    headers["OpenAI-Project"] = OPENAI_PROJECT;
  }

  // For providers that don't require an API key (like Ollama) or where API key is missing,
  // we'll use a placeholder to avoid throwing errors when creating the client
  const finalApiKey = apiKey || (provider === "ollama" ? "dummy" : "placeholder-key-will-fail");
  
  if (provider === "azure") {
    return new AzureOpenAI({
      apiKey: finalApiKey,
      baseURL: getBaseUrl(provider),
      apiVersion: AZURE_OPENAI_API_VERSION,
      timeout: OPENAI_TIMEOUT_MS,
      defaultHeaders: headers,
    });
  }

  return new OpenAI({
    apiKey: finalApiKey,
    baseURL: getBaseUrl(provider),
    timeout: OPENAI_TIMEOUT_MS,
    defaultHeaders: headers,
  });
}
